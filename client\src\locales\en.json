{"staff": {"management": "Staff Management", "overview": "Staff Overview", "description": "Manage staff members, departments, roles and permissions across the organization.", "addStaffMember": "Add Staff Member", "searchStaff": "Search staff name or email...", "allRoles": "All Roles", "allDepartments": "All Departments", "allStatus": "All Status", "name": "Name", "email": "Email", "role": "Role", "department": "Department", "status": "Status", "joinDate": "Join Date", "actions": "Actions", "active": "Active", "inactive": "Inactive", "pending": "Pending", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "cancel": "Cancel"}, "departments": {"management": "Department Management", "description": "Manage organizational structure and department configuration", "addDepartment": "Add Department", "editDepartment": "Edit Department", "createDepartment": "Create Department", "updateDepartment": "Update Department", "deleteDepartment": "Delete Department", "totalDepartments": "Total Departments", "totalEmployees": "Total Employees", "totalBudget": "Total Budget", "avgTeamSize": "Avg. Team Size", "departmentName": "Department Name", "manager": "Manager", "location": "Location", "phone": "Phone", "budget": "Budget", "employeeCount": "Employee Count", "createdAt": "Created At", "searchDepartments": "Search departments, managers, or descriptions...", "filterByStatus": "Filter by status", "addNewDepartment": "Add New Department", "createNewDepartment": "Create a new department for your organization", "updateDepartmentInfo": "Update department information and settings", "briefDescription": "Brief description of department responsibilities", "departmentHead": "Department head", "annualBudget": "Annual budget", "physicalLocation": "Physical location or office", "acrossAllDepartments": "Across all departments", "employeesPerDepartment": "Employees per department", "manageDepartmentStructure": "Manage your organization's department structure and assignments", "noDepartmentsFound": "No departments found", "adjustSearchCriteria": "Try adjusting your search criteria", "addFirstDepartment": "Add your first department to get started", "departmentCreatedSuccess": "Department created successfully", "departmentUpdatedSuccess": "Department information updated successfully", "departmentDeletedSuccess": "Department deleted successfully", "departmentStatusChanged": "Department status changed", "fillRequiredFields": "Please fill in required fields", "confirmDeleteDepartment": "Are you sure you want to delete this department? This action cannot be undone and will affect employees.", "employees": "employees", "annualBudgetAllocation": "Annual budget allocation", "notSpecified": "Not specified"}, "roles": {"management": "Roles & Permissions", "description": "Manage system roles and access permissions", "addRole": "Add Role", "editRole": "Edit Role", "createRole": "Create Role", "updateRole": "Update Role", "deleteRole": "Delete Role", "totalRoles": "Total Roles", "totalUsers": "Total Users", "totalPermissions": "Total Permissions", "customRoles": "Custom Roles", "roleName": "Role Name", "roleDescription": "Role Description", "permissions": "Permissions", "users": "Users", "type": "Type", "level": "Level", "system": "System", "custom": "Custom", "admin": "Admin", "manager": "Manager", "user": "User", "viewer": "Viewer", "roles": "Roles", "userAssignments": "User Assignments", "systemRoles": "System Roles", "manageRolesPermissions": "Manage roles and their permissions across the system", "systemPermissions": "System Permissions", "overviewAllPermissions": "Overview of all available permissions in the system", "userRoleAssignments": "User Role Assignments", "manageUserRoleAssignments": "Manage role assignments for individual users", "currentRole": "Current Role", "changeRole": "Change Role", "createNewRole": "Create a new role with specific permissions", "updateRoleInfo": "Update role information and permissions", "briefRoleDescription": "Brief description of role responsibilities", "withAssignedRoles": "With assigned roles", "acrossCategories": "Across categories", "createdByUsers": "Created by users", "searchRoles": "Search roles...", "noRolesFound": "No roles found", "createFirstCustomRole": "Create your first custom role", "roleCreatedSuccess": "Role created successfully", "roleUpdatedSuccess": "Role updated successfully", "roleDeletedSuccess": "Role deleted successfully", "cannotDeleteSystemRole": "Cannot delete system role", "confirmDeleteRole": "Are you sure you want to delete this role? This action cannot be undone and will affect users.", "fillRoleNameDescription": "Please fill in role name and description"}, "permissions": {"userManagement": "User Management", "contentManagement": "Content Management", "analytics": "Analytics", "systemSettings": "System Settings", "financial": "Financial", "apiAccess": "API Access", "createUsers": "Create Users", "viewUsers": "View Users", "editUsers": "Edit Users", "deleteUsers": "Delete Users", "createContent": "Create Content", "viewContent": "View Content", "editContent": "Edit Content", "deleteContent": "Delete Content", "publishContent": "Publish Content", "viewAnalytics": "View Analytics", "exportAnalytics": "Export Analytics", "viewSettings": "View Settings", "manageSettings": "Manage Settings", "securitySettings": "Security Settings", "viewBilling": "View Billing", "manageBilling": "Manage Billing", "apiReadAccess": "API Read Access", "apiWriteAccess": "API Write Access", "apiAdminAccess": "API Admin Access"}, "header": {"notifications": "Notifications", "profile": "Profile", "logOut": "Log out", "home": "Home", "newFeatureReleased": "New feature released!", "checkLatestUpdates": "Check out the latest updates to our platform.", "systemMaintenance": "System Maintenance", "scheduledMaintenance": "Scheduled for tomorrow at 2 AM.", "viewAllNotifications": "View all notifications"}, "homePage": {"nav": {"home": "Home", "features": "Features", "pricing": "Pricing", "about": "About", "contact": "Contact"}, "hero": {"title": "AI-Powered Content Creation Platform", "subtitleRegular": "Transform your ideas into ", "subtitleGradient": "beautiful digital experiences", "description": "Transform your content creation process with our advanced AI platform. Generate, optimize, and distribute engaging content across multiple channels in minutes, not hours.", "cta": "Get Started Free", "dynamicSubtitle": "Experience the future of interactive design with dynamic pattern generation", "explorePatterns": "Explore Patterns", "currentPattern": "Current Pattern: "}, "features": {"title": "Powerful Features for Modern Content Creation", "description": "Everything you need to create, optimize, and distribute content that engages your audience and drives results.", "items": {"contentGeneration": {"title": "AI-Powered Content Generation", "description": "Advanced AI algorithms to create high-quality, engaging content that resonates with your audience."}, "easeOfUse": {"title": "Ease of use", "description": "Intuitive interface designed for creators of all skill levels. Start creating amazing content in minutes."}, "flexiblePricing": {"title": "Flexible Pricing", "description": "Affordable plans that scale with your needs. Start free, upgrade when you're ready."}, "cloudBased": {"title": "Cloud-based Platform", "description": "Access your content creation tools anywhere, anytime with 99.9% uptime guarantee."}, "multiPlatform": {"title": "Multi-platform Distribution", "description": "Seamlessly distribute your content across multiple platforms with one-click publishing."}, "support": {"title": "24/7 Customer Support", "description": "Our dedicated support team is always here to help you succeed with your content strategy."}, "optimization": {"title": "Smart Optimization", "description": "Intelligent content optimization based on performance analytics and audience insights."}, "builtWithLove": {"title": "Built with Love", "description": "Crafted by content creators, for content creators. We understand your needs."}}}, "integrations": {"title": "Powerful Platform Integrations", "description": "Connect with your favorite platforms and tools to create seamless automated workflows", "footer": {"title": "Seamless Integration Experience", "description": "Connect with hundreds of third-party applications and services through Webhook and API integrations, making your business processes more intelligent and automated."}}, "testimonials": {"title": {"whatOur": "What Our", "customersSay": "Customers Say"}, "subtitle": "Join thousands of satisfied customers who have transformed their content creation process. Based on {{count}} reviews with an average rating of {{rating}} out of 5 stars.", "reviews": [{"text": "iTeraBiz transformed our content creation process. We now produce 3x more content in half the time.", "author": {"name": "<PERSON>", "title": "Marketing Director"}}, {"text": "The AI insights are incredible. Our engagement rates increased by 400% since using iTeraBiz.", "author": {"name": "<PERSON>", "title": "CEO"}}, {"text": "Best investment we made this year. The ROI is phenomenal and the support team is amazing.", "author": {"name": "<PERSON>", "title": "Content Manager"}}, {"text": "Exceptional platform that has revolutionized how we approach content strategy and execution.", "author": {"name": "<PERSON>", "title": "Marketing VP"}}, {"text": "The automation features have saved us countless hours while improving our content quality.", "author": {"name": "<PERSON>", "title": "Digital Director"}}, {"text": "Outstanding results! Our team productivity increased by 300% with iTeraBiz implementation.", "author": {"name": "<PERSON>", "title": "Operations Manager"}}], "stats": {"average": "{{value}} average", "reviews": "{{count}}+ reviews", "satisfaction": "95% satisfaction"}}, "pricing": {"title": "Choose Your Perfect Plan", "description": "Scale your content creation with our AI-powered platform\nAll plans include access to our advanced AI tools, analytics, and dedicated support.", "monthly": "Monthly", "annualBilling": "Annual billing", "save20": "Save 20%", "billing": {"monthly": "Monthly", "annualBilling": "Annual billing", "save20": "Save 20%"}, "plans": {"starter": {"name": "STARTER", "price": "29", "yearlyPrice": "23", "period": "per month", "description": "Perfect for individuals and small content creators", "buttonText": "Start Free Trial", "features": ["10,000 AI-generated words/month", "5 Content templates", "Basic analytics", "Email support", "1 User account", "Standard content quality"]}, "professional": {"name": "PROFESSIONAL", "price": "79", "yearlyPrice": "63", "period": "per month", "description": "Ideal for growing teams and content agencies", "buttonText": "Get Started", "features": ["50,000 AI-generated words/month", "25+ Content templates", "Advanced analytics & insights", "Priority support", "5 User accounts", "API access", "Custom branding", "Multi-platform publishing"]}, "enterprise": {"name": "ENTERPRISE", "price": "199", "yearlyPrice": "159", "period": "per month", "description": "For large organizations with enterprise needs", "buttonText": "Contact Sales", "features": ["Unlimited AI-generated content", "Custom templates & workflows", "Dedicated account manager", "White-label solution", "Unlimited users", "Advanced API & integrations", "SLA guarantee", "Custom AI model training", "Priority feature requests"]}}}, "cta": {"title": "Ready to Transform?", "subtitle": "Join thousands of creators who trust iTeraBiz for their content needs", "buttonText": "Start Your Journey", "ctaText": "Start Your Journey →", "transformTitle": "Ready to Transform Your Content Creation?", "transformSubtitle": "Join thousands of content creators who are already using AI to create amazing content. Start your free trial today.", "startFreeTrial": "Start Free Trial", "contactSales": "Contact Sales", "watchDemo": "Watch Demo"}, "footer": {"followUs": "Follow us", "copyright": "© 2025 iTeraBiz. All rights reserved.", "brand": "iTeraBiz", "logoAlt": "iTeraBiz Logo", "socialLinks": {"facebook": "Follow us on Facebook", "twitter": "Follow us on Twitter", "linkedin": "Follow us on LinkedIn"}}}, "overview": {"title": "Business Overview", "description": "Welcome back! Quickly understand your business status and key indicators", "viewDetailedAnalysis": "View Detailed Analysis", "coreBusinessIndicators": "Core Business Indicators", "viewTrendAnalysis": "View Trend Analysis", "quickNavigation": "Quick Navigation", "selectFunctionModule": "Select the function module you want to view", "totalUsers": "Total Users", "activeUsers": "Active Users", "conversionRate": "Conversion Rate", "monthlyGrowth": "Monthly Growth", "dailyActiveUsers": "Daily Active Users", "monthlyImprovement": "Monthly Improvement", "systemMonitoring": "System Monitoring", "systemMonitoringDesc": "Real-time system status and performance monitoring", "businessAnalytics": "Business Analytics", "businessAnalyticsDesc": "Usage analysis and trend insights for business modules", "dataInsights": "Data Insights", "dataInsightsDesc": "AI-driven deep data insights for agents and user behavior", "aiAgentManagement": "AI Agent Management", "aiAgentManagementDesc": "Create and manage AI agents", "bookingSystem": "Booking System", "bookingSystemDesc": "Appointment management and service scheduling", "platformIntegration": "Platform Integration", "platformIntegrationDesc": "Third-party platform API integration management", "live": "Live", "insights": "Insights", "ai": "AI", "management": "Management", "service": "Service", "integration": "Integration", "uptime": "99.9% Uptime", "moduleAnalysis": "4 Module Analysis", "analysisDimensions": "7 Analysis Dimensions", "activeAgents": "6 Active Agents", "appointments": "156 Appointments", "platforms": "3 Platforms", "todaysOverview": "Today's Overview", "todaysVisits": "Today's Visits", "aiInteraction": "AI Interactions", "apiCalls": "API Calls", "successfulBookings": "Successful Bookings", "failedBookings": "Failed Bookings", "totalRevenue": "Total Revenue", "avgResponseTime": "Avg. Response Time", "errorRate": "Error Rate"}}