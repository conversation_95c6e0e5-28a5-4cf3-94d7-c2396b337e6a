{"staff": {"management": "Pengurusan Staf", "overview": "<PERSON>kh<PERSON>ar Staf", "description": "<PERSON><PERSON> ahli staf, j<PERSON><PERSON>, peranan dan kebenaran di seluruh organisasi.", "addStaffMember": "Tambah Ahli Staf", "searchStaff": "Cari nama atau emel staf...", "allRoles": "<PERSON><PERSON><PERSON>", "allDepartments": "<PERSON><PERSON><PERSON>", "allStatus": "Semua Status", "name": "<PERSON><PERSON>", "email": "<PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>", "department": "Jabatan", "status": "Status", "joinDate": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>", "active": "Aktif", "inactive": "Tidak Aktif", "pending": "<PERSON><PERSON><PERSON>", "edit": "Edit", "delete": "Padam", "confirm": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON>"}, "departments": {"management": "Pen<PERSON>rusan <PERSON>", "description": "Penerangan", "addDepartment": "Tambah Jabatan", "editDepartment": "<PERSON>", "createDepartment": "Cipta <PERSON>", "updateDepartment": "Kemaskini Jabatan", "deleteDepartment": "Padam Jabatan", "totalDepartments": "<PERSON><PERSON><PERSON>", "totalEmployees": "<PERSON><PERSON><PERSON>", "totalBudget": "<PERSON><PERSON><PERSON>", "avgTeamSize": "<PERSON><PERSON><PERSON>", "departmentName": "<PERSON><PERSON>", "manager": "<PERSON><PERSON><PERSON>", "location": "<PERSON><PERSON>", "phone": "Telefon", "budget": "Bajet", "employeeCount": "Bilangan Pekerja", "createdAt": "<PERSON><PERSON><PERSON>", "searchDepartments": "<PERSON>i j<PERSON>, pengu<PERSON>, atau penerangan...", "filterByStatus": "Tapis mengikut status", "addNewDepartment": "Tambah Jabatan Baru", "createNewDepartment": "Cipta jabatan baru untuk organisasi anda", "updateDepartmentInfo": "Kemaskini maklumat dan tetapan jabatan", "briefDescription": "Penerangan ringkas tentang tanggungjawab jabatan", "departmentHead": "<PERSON><PERSON><PERSON> j<PERSON>", "annualBudget": "<PERSON><PERSON>", "physicalLocation": "Lokasi fizikal atau pejabat", "acrossAllDepartments": "<PERSON><PERSON><PERSON> semua j<PERSON>", "employeesPerDepartment": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "manageDepartmentStructure": "<PERSON><PERSON> struktur jabatan organisasi anda dan tugasan", "noDepartmentsFound": "Tiada jabatan dijumpai", "adjustSearchCriteria": "Cuba laraskan kriteria carian anda", "addFirstDepartment": "Tambah jabatan pertama anda untuk bermula", "departmentCreatedSuccess": "<PERSON><PERSON><PERSON> ber<PERSON>a dicipta", "departmentUpdatedSuccess": "Mak<PERSON>at jabatan ber<PERSON>a <PERSON>", "departmentDeletedSuccess": "<PERSON><PERSON><PERSON> berjaya dipadam", "departmentStatusChanged": "Status jabatan berubah", "fillRequiredFields": "Sila isi medan yang diperlukan", "confirmDeleteDepartment": "<PERSON><PERSON><PERSON> anda pasti mahu memadam jabatan ini? Tindakan ini tidak boleh dibatalkan dan akan menje<PERSON> pekerja.", "employees": "<PERSON><PERSON><PERSON><PERSON>", "annualBudgetAllocation": "<PERSON><PERSON><PERSON><PERSON> bajet ta<PERSON>an", "notSpecified": "Tidak dinyatakan"}, "roles": {"management": "Peranan & Kebenaran", "description": "<PERSON><PERSON> peranan sistem dan kebenaran capaian", "addRole": "Tambah Peranan", "editRole": "<PERSON>", "createRole": "<PERSON><PERSON><PERSON>", "updateRole": "<PERSON><PERSON><PERSON>", "deleteRole": "<PERSON><PERSON>", "totalRoles": "<PERSON><PERSON><PERSON>", "totalUsers": "<PERSON><PERSON><PERSON>", "totalPermissions": "<PERSON><PERSON><PERSON>", "customRoles": "<PERSON><PERSON><PERSON>", "roleName": "<PERSON><PERSON>", "roleDescription": "<PERSON><PERSON><PERSON>", "permissions": "<PERSON><PERSON><PERSON>", "users": "Pengguna", "type": "<PERSON><PERSON>", "level": "<PERSON><PERSON><PERSON>", "system": "Sistem", "custom": "Tersuai", "admin": "Admin", "manager": "<PERSON><PERSON><PERSON>", "user": "Pengguna", "viewer": "<PERSON><PERSON><PERSON>", "roles": "<PERSON><PERSON><PERSON>", "userAssignments": "<PERSON>gas<PERSON>", "systemRoles": "peranan sistem", "manageRolesPermissions": "<PERSON><PERSON> peranan dan kebenaran mereka di seluruh sistem", "systemPermissions": "Kebenaran Sistem", "overviewAllPermissions": "<PERSON><PERSON><PERSON><PERSON> semua kebenaran yang tersedia dalam sistem", "userRoleAssignments": "<PERSON><PERSON><PERSON>", "manageUserRoleAssignments": "<PERSON>rus tugasan peranan untuk pengguna individu", "currentRole": "<PERSON><PERSON><PERSON>", "changeRole": "<PERSON><PERSON>", "createNewRole": "<PERSON><PERSON><PERSON> peranan baru dengan kebenaran khusus", "updateRoleInfo": "Kemas<PERSON> maklumat peranan dan kebenaran", "briefRoleDescription": "Penerangan ringkas tentang tanggungjawab peranan", "withAssignedRoles": "<PERSON>gan peranan yang ditetapkan", "acrossCategories": "<PERSON><PERSON><PERSON> ka<PERSON>", "createdByUsers": "<PERSON><PERSON><PERSON> o<PERSON>h <PERSON>una", "searchRoles": "<PERSON>i peranan...", "noRolesFound": "<PERSON><PERSON><PERSON> peranan di<PERSON>ai", "createFirstCustomRole": "<PERSON><PERSON><PERSON> peranan tersuai pertama anda", "roleCreatedSuccess": "<PERSON><PERSON><PERSON> be<PERSON><PERSON> dicipta", "roleUpdatedSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "roleDeletedSuccess": "<PERSON><PERSON><PERSON> ber<PERSON>a <PERSON>adam", "cannotDeleteSystemRole": "Tidak boleh memadam peranan sistem", "confirmDeleteRole": "<PERSON><PERSON><PERSON> anda pasti mahu memadam peranan ini? Tindakan ini tidak boleh dibatalkan dan akan menjejaskan pengguna.", "fillRoleNameDescription": "Sila isi nama peranan dan penerangan"}, "permissions": {"userManagement": "<PERSON><PERSON><PERSON><PERSON>", "contentManagement": "<PERSON><PERSON><PERSON><PERSON>", "analytics": "<PERSON><PERSON><PERSON>", "systemSettings": "Tetapan <PERSON>", "financial": "<PERSON><PERSON><PERSON>", "apiAccess": "Capaian API", "createUsers": "Cipta <PERSON>", "viewUsers": "<PERSON><PERSON>", "editUsers": "<PERSON>", "deleteUsers": "Padam Pengguna", "createContent": "<PERSON><PERSON><PERSON>", "viewContent": "<PERSON><PERSON>", "editContent": "<PERSON>", "deleteContent": "<PERSON><PERSON>", "publishContent": "Terbitkan Kandungan", "viewAnalytics": "<PERSON><PERSON>", "exportAnalytics": "Eksport Analitik", "viewSettings": "<PERSON><PERSON>", "manageSettings": "<PERSON><PERSON>", "securitySettings": "Tetapan <PERSON>", "viewBilling": "<PERSON><PERSON>", "manageBilling": "<PERSON><PERSON>", "apiReadAccess": "Capaian Baca API", "apiWriteAccess": "Capaian Tulis API", "apiAdminAccess": "Capaian Admin API"}, "header": {"notifications": "Pemberitahuan", "profile": "Profil", "logOut": "Log keluar", "home": "<PERSON><PERSON><PERSON>", "newFeatureReleased": "Ciri baru dikeluarkan!", "checkLatestUpdates": "<PERSON>hat kemas kini terkini platform kami.", "systemMaintenance": "Penyelenggaraan Si<PERSON>", "scheduledMaintenance": "Dijadualkan untuk esok pada 2 Pagi.", "viewAllNotifications": "<PERSON><PERSON> semua pember<PERSON>n"}, "overview": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Selamat kembali! Fahami status perniagaan dan penunjuk utama anda dengan cepat", "viewDetailedAnalysis": "<PERSON><PERSON>", "coreBusinessIndicators": "Penunjuk <PERSON>", "viewTrendAnalysis": "<PERSON><PERSON>", "quickNavigation": "Navigasi <PERSON>", "selectFunctionModule": "<PERSON><PERSON>h modul fungsi yang ingin anda lihat", "totalUsers": "<PERSON><PERSON><PERSON>", "activeUsers": "Pengguna Aktif", "conversionRate": "<PERSON><PERSON>", "monthlyGrowth": "<PERSON><PERSON><PERSON><PERSON>", "dailyActiveUsers": "Pengguna Aktif <PERSON>", "monthlyImprovement": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "systemMonitoring": "Pemantauan Sistem", "systemMonitoringDesc": "Pemantauan status sistem dan prestasi masa nyata", "businessAnalytics": "<PERSON><PERSON><PERSON>", "businessAnalyticsDesc": "<PERSON><PERSON><PERSON> pen<PERSON>an dan wawasan trend untuk modul perniagaan", "dataInsights": "Wawasan Data", "dataInsightsDesc": "Wawasan data mendalam didorong AI untuk ejen dan tingkah laku pengguna", "aiAgentManagement": "<PERSON><PERSON><PERSON><PERSON>", "aiAgentManagementDesc": "<PERSON><PERSON><PERSON> dan urus ejen <PERSON>", "bookingSystem": "Sistem Tempahan", "bookingSystemDesc": "<PERSON><PERSON><PERSON><PERSON> janji temu dan penja<PERSON> perkhidmatan", "platformIntegration": "Integrasi Platform", "platformIntegrationDesc": "Pengurusan integrasi API platform pihak ketiga", "live": "Langsung", "insights": "<PERSON><PERSON><PERSON>", "ai": "AI", "management": "<PERSON><PERSON><PERSON><PERSON>", "service": "Perkhidmatan", "integration": "Integrasi", "uptime": "99.9% Masa Aktif", "moduleAnalysis": "4 <PERSON><PERSON><PERSON>", "analysisDimensions": "7 Dimensi <PERSON>is", "activeAgents": "6 Ejen Aktif", "appointments": "156 <PERSON><PERSON>", "platforms": "3 Platform", "todaysOverview": "<PERSON><PERSON><PERSON><PERSON>", "todaysVisits": "<PERSON><PERSON><PERSON><PERSON>", "aiInteraction": "Interaksi AI", "newAppointment": "<PERSON><PERSON>", "newLead": "Pet<PERSON><PERSON><PERSON>"}, "navigation": {"overview": "<PERSON><PERSON><PERSON><PERSON>", "dataInsight": "Wawasan Data", "agents": "<PERSON><PERSON><PERSON>", "platformApi": "API Platform", "billing": "<PERSON><PERSON><PERSON><PERSON>", "analytics": "<PERSON><PERSON><PERSON>", "staff": "Staf", "systemSettings": "Tetapan <PERSON>", "profile": "Profil", "chat": "Sembang", "booking": "<PERSON><PERSON><PERSON>", "settings": "Tetapan", "dashboard": "<PERSON><PERSON>", "dataInsights": "Wawasan Data", "userAnalytics": "<PERSON><PERSON><PERSON>", "businessAnalytics": "<PERSON><PERSON><PERSON>", "contentData": "Data Ka<PERSON>", "modules": "<PERSON><PERSON><PERSON>", "aiAutoReply": "Balasan Auto AI", "walkinBooking": "<PERSON><PERSON>ahan Walk-in", "onsiteBooking": "Tempahan Di Tapak", "contentGenerator": "<PERSON><PERSON>", "integrations": "Integrasi", "staffManagement": "Pengurusan Staf", "general": "<PERSON><PERSON>", "account": "<PERSON><PERSON><PERSON>", "branding": "<PERSON><PERSON><PERSON><PERSON>", "moduleConfig": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "notifications": "Pemberitahuan"}, "common": {"save": "Simpan", "cancel": "<PERSON><PERSON>", "edit": "Edit", "delete": "Padam", "create": "Cip<PERSON>", "update": "Kemaskini", "search": "<PERSON><PERSON>", "filter": "<PERSON><PERSON>", "loading": "Memuatkan...", "error": "<PERSON><PERSON>", "success": "<PERSON><PERSON><PERSON><PERSON>", "warning": "<PERSON><PERSON>", "info": "Maklumat", "required": "<PERSON><PERSON><PERSON><PERSON>", "optional": "<PERSON><PERSON><PERSON>", "yes": "Ya", "no": "Tidak", "close": "<PERSON><PERSON><PERSON>", "open": "<PERSON><PERSON>", "activate": "Aktifkan", "deactivate": "Nyahaktifkan", "refresh": "<PERSON><PERSON>", "retry": "Cuba Lagi"}, "settings": {"manageGlobalSettings": "Urus dan konfigurasi tetapan global untuk sistem.", "basicSettings": "<PERSON><PERSON><PERSON>", "basicSettingsDesc": "Konfigu<PERSON><PERSON> mak<PERSON>at asas dan keutamaan untuk sistem.", "systemName": "<PERSON><PERSON>", "systemDescription": "Penerangan Sistem", "timezone": "<PERSON><PERSON>", "selectTimezone": "<PERSON><PERSON><PERSON>", "dateFormat": "Format Tarikh", "selectDateFormat": "Pilih Format Tarikh", "logoUrl": "URL Logo", "logoRecommendedSize": "Saiz yang disyorkan: 200px x 50px", "languageSettings": "Tetapan <PERSON>", "languageSettingsDesc": "<PERSON><PERSON><PERSON> bahasa antara muka untuk aplikasi.", "interfaceLanguage": "Bahasa Antara <PERSON>", "languageChangeNote": "<PERSON><PERSON><PERSON> bahasa akan berkuat kuasa dengan serta-merta di seluruh aplikasi.", "security": "Keselamatan", "dataManagement": "Pengurusan Data", "settingsSaved": "<PERSON><PERSON><PERSON> sistem anda telah ber<PERSON>.", "settingsReset": "Tetapan <PERSON>", "settingsResetToDefault": "<PERSON>tapan telah direset kepada nilai lalai."}, "dataInsight": {"title": "<PERSON><PERSON>", "description": "Analitik berkuasa AI dan wawasan pintar", "exportReport": "Eksport Laporan", "aiInsights": "Wawasan AI", "userBehavior": "Tingkah <PERSON>", "agents": "<PERSON><PERSON><PERSON>", "leads": "Petunjuk", "bookings": "<PERSON><PERSON><PERSON>", "advanced": "Lanjutan", "userEngagementChange": "Perubahan Corak Penglibatan Pengguna", "userEngagementChangeDesc": "Mengesan peningkatan 23% dalam tempoh sesi pengguna sepanjang minggu lepas, teru<PERSON><PERSON>a pada waktu petang (6-9 PTG).", "leadQualityTrend": "Trend Peningkatan Ku<PERSON>", "leadQualityTrendDesc": "Model AI meramalkan kualiti petunjuk akan meningkat 18% dalam 30 hari akan datang berdasarkan corak pengoptimuman semasa.", "churnRiskAlert": "<PERSON><PERSON>", "churnRiskAlertDesc": "Mengenal pasti 15 pengguna yang menunjukkan penunjuk churn awal berdasarkan aktiviti dan corak penglibatan yang menurun.", "highConfidence": "<PERSON><PERSON><PERSON>", "mediumConfidence": "<PERSON><PERSON><PERSON>", "conversionRateIncrease": "Potensi peningkatan 15% dalam kadar penukaran", "increaseMarketingActivities": "Pertimbangkan untuk meningkatkan aktiviti pemasaran semasa waktu penglibatan puncak", "salesConversionIncrease": "Dijangka peningkatan 12% dalam penukaran jualan", "prepareSalesTeam": "Sediakan pasukan jualan untuk volum petunjuk berkualiti tinggi yang meningkat", "potentialRevenueLoss": "Po<PERSON>i kehilangan hasil $12,000", "implementRetentionCampaigns": "Laksanakan kempen pengekalan yang disasarkan untuk pengguna berisiko", "userBehaviorPrediction": "<PERSON><PERSON>", "leadScoringModel": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "contentOptimization": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "customerRetention": "<PERSON><PERSON>kal<PERSON>", "hoursAgo": "{{count}} jam yang lalu", "dayAgo": "{{count}} hari yang lalu", "identifiedHighRiskUsers": "Mengenal pasti {{count}} pengguna berisiko tinggi", "bookingIncreaseExpected": "Dijangka peningkatan 12% dalam tempahan minggu depan", "contentPerformanceImprovement": "Prestasi kandungan akan meningkat sebanyak 15%", "retentionRateExpected": "<PERSON><PERSON> pen<PERSON> dijangka mencapai 92%", "potentialImpact": "Impak Berpotensi", "behaviorAnalysis": "<PERSON><PERSON><PERSON>", "trendPrediction": "Ramalan Trend", "riskIdentification": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activeModels": "Model Aktif", "aiAnalysisActive": "Analisis AI Aktif", "averageAccuracy": "<PERSON><PERSON><PERSON>", "averageModelAccuracy": "<PERSON><PERSON>ta ketepatan model merentas semua sistem AI", "predictionsPerHour": "Ramalan/Jam", "lastWeekComparison": "berbanding minggu lepas", "realTimePredictionProcessing": "<PERSON><PERSON><PERSON><PERSON><PERSON> ramalan masa nyata", "exportingReport": "Mengeksport laporan wawasan AI yang komprehensif...", "customerSupport": "Sokongan Pelanggan", "salesAssistant": "Pembantu Jualan", "technicalSupport": "Sokongan Teknikal", "leadQualification": "<PERSON><PERSON><PERSON><PERSON>"}, "platformApi": {"title": "Integrasi Platform", "description": "Sambung dan urus integrasi API dengan platform dan perkhidmatan pihak ketiga. Pemantauan status masa nyata dan sokongan webhook.", "searchIntegrations": "Cari integrasi...", "connected": "Disambung", "addNewIntegration": "Tambah Integrasi Baru", "discoverMore": "<PERSON><PERSON><PERSON>", "webhookConfiguration": "Dialog konfigu<PERSON> (<PERSON><PERSON>)", "manage": "Urus", "disconnect": "<PERSON><PERSON><PERSON>", "resolveIssues": "<PERSON><PERSON><PERSON><PERSON> isu dengan", "confirmDisconnect": "<PERSON><PERSON><PERSON> anda pasti mahu memutuskan sambungan dari", "disconnectSuccess": "<PERSON><PERSON><PERSON><PERSON> memu<PERSON>kan sambungan dari", "operationFailed": "<PERSON><PERSON> gagal", "discoverModal": "Buka modal terokai/tambah integrasi baru (<PERSON><PERSON>)", "refreshFailed": "<PERSON><PERSON> semula gagal", "loadingPlatforms": "Memuatkan maklumat platform...", "browseAndConnect": "Layari dan sambung lebih banyak platform untuk mengembangkan jangkauan perniagaan anda.", "foundIntegrations": "Dijumpai", "integrationsMatching": "integrasi yang sepadan", "all": "<PERSON><PERSON><PERSON>", "productivity": "Produktiviti", "social": "Sosial", "communication": "<PERSON><PERSON><PERSON><PERSON>", "ecommerce": "<PERSON><PERSON><PERSON><PERSON>g"}, "billing": {"title": "Pusat <PERSON>", "description": "<PERSON><PERSON> la<PERSON>, pen<PERSON><PERSON><PERSON>, dan ka<PERSON>h pem<PERSON> anda", "overview": "<PERSON><PERSON><PERSON><PERSON>", "subscription": "<PERSON><PERSON><PERSON>", "billingHistory": "<PERSON><PERSON><PERSON>", "paymentMethods": "<PERSON><PERSON><PERSON>", "downloadInvoice": "<PERSON><PERSON>", "refreshData": "Muat Semula Data", "viewDetails": "<PERSON><PERSON>", "managePayment": "<PERSON><PERSON>", "thisMonth": "<PERSON><PERSON><PERSON>", "nextBilling": "<PERSON><PERSON><PERSON><PERSON>", "paymentStatus": "Status Pembayaran", "currentPlan": "<PERSON><PERSON><PERSON>", "active": "Aktif", "lastPaymentSuccessful": "<PERSON>embayaran terakhir berjaya", "monthlyUsageOverview": "<PERSON><PERSON><PERSON><PERSON>", "monitorUsageToAvoidOverage": "<PERSON><PERSON><PERSON> pengg<PERSON>an sumber anda untuk mengelakkan caj berlebihan", "apiCalls": "Panggilan API", "storageSpace": "<PERSON><PERSON>", "aiProcessing": "Pemprosesan AI", "bandwidthUsage": "<PERSON><PERSON><PERSON><PERSON>", "billingAlerts": "<PERSON><PERSON>", "apiUsageNearingLimit": "Penggunaan API hampir mencapai had", "apiUsageLimitWarning": "Penggunaan API anda telah mencapai 78% daripada had bulanan. Pertimbangkan untuk menaik taraf pelan anda.", "nextAutoPaymentReminder": "Peringatan pembayaran automatik seterusnya", "upgrade": "<PERSON><PERSON>", "manage": "Urus", "addPaymentMethod": "<PERSON><PERSON>", "deletePaymentMethod": "<PERSON><PERSON>", "confirmDeletePayment": "<PERSON><PERSON>h anda pasti mahu memadam kaedah pembayaran ini?", "downloadInvoiceFor": "Muat turun invois untuk", "starter": "<PERSON><PERSON><PERSON>", "professional": "Profesional", "enterprise": "<PERSON><PERSON><PERSON><PERSON>", "conversations": "perbualan/bulan", "platforms": "integrasi platform", "teamMembers": "ahli pasukan", "unlimited": "<PERSON><PERSON>", "basicAnalytics": "<PERSON><PERSON><PERSON>", "emailSupport": "Sokongan emel", "advancedAnalytics": "<PERSON><PERSON><PERSON>", "prioritySupport": "Sokongan keutamaan", "whatsappIncluded": "WhatsApp Business disertakan", "customAnalytics": "<PERSON><PERSON><PERSON>", "dedicatedSupport": "Sokongan khusus 24/7", "slaGuarantee": "Jaminan SLA", "whiteLabelCustomization": "Penyesuaian label putih", "billing": {"monthly": "Bulanan", "annualBilling": "<PERSON><PERSON>", "save20": "Jimat 20%"}}, "analytics": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> komprehensif dan wawasan data", "overview": "<PERSON><PERSON><PERSON><PERSON>", "modules": "<PERSON><PERSON><PERSON>", "prediction": "<PERSON><PERSON>", "performance": "<PERSON><PERSON><PERSON>", "businessAnalytics": "<PERSON><PERSON><PERSON>", "moduleUsageTrends": "Trend <PERSON>", "usageTrendsDescription": "<PERSON><PERSON><PERSON> pengg<PERSON>an masa nyata merentasi semua modul", "performanceComparison": "Perband<PERSON><PERSON>", "performanceComparisonDescription": "<PERSON><PERSON><PERSON><PERSON> masa respons dan kadar kejayaan antara modul", "usageDistribution": "<PERSON><PERSON><PERSON>", "moduleGrowthRate": "<PERSON><PERSON>", "moduleGrowthDescription": "<PERSON><PERSON> relatif untuk setiap modul", "aiChatbot": "AI Chatbot", "bookingSystem": "Sistem Tempahan", "contentGeneration": "<PERSON><PERSON><PERSON>", "platformApi": "API Platform", "responseTime": "<PERSON><PERSON>", "successRate": "<PERSON><PERSON>", "userSatisfaction": "Kepuasan Pengguna", "conversionRate": "<PERSON><PERSON>", "exportReport": "Eksport Laporan", "last7Days": "7 hari lepas", "last30Days": "30 hari lepas", "last90Days": "90 hari lepas", "all": "<PERSON><PERSON><PERSON>", "chatBot": "<PERSON><PERSON>", "booking": "<PERSON><PERSON><PERSON>", "content": "Kandungan", "api": "API"}, "agents": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>, urus dan pantau ejen AI anda", "createAgent": "<PERSON><PERSON><PERSON>", "smartWizard": "Wizard Pintar", "agentList": "<PERSON><PERSON><PERSON>", "searchAgents": "<PERSON>i ejen...", "filterByStatus": "Tapis mengikut status", "filterByType": "<PERSON><PERSON> mengikut jenis", "allStatuses": "Semua Status", "allTypes": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON>", "type": "<PERSON><PERSON>", "status": "Status", "created": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>", "active": "Aktif", "inactive": "Tidak Aktif", "draft": "Draf", "error": "<PERSON><PERSON>", "deploying": "Menggunakan", "activate": "Aktifkan", "deactivate": "Nyahaktifkan", "edit": "Edit", "delete": "Padam", "view": "Lihat", "confirmDelete": "<PERSON><PERSON>h anda pasti mahu memadam ejen ini?", "deleteDescription": "Tindakan ini tidak boleh dibatalkan. Ini akan memadam ejen secara kekal dan semua data yang berkaitan.", "statusUpdated": "Status Dikemaskini", "statusUpdateFailed": "<PERSON><PERSON>", "agentDeleted": "<PERSON><PERSON><PERSON>", "deletionFailed": "<PERSON><PERSON><PERSON><PERSON>", "aiAutoReply": "Balasan Auto AI", "contentGenerator": "<PERSON><PERSON>", "bookingOnsite": "Tempahan Di Tapak", "bookingWalkin": "<PERSON><PERSON>ahan Walk-in", "leadGeneration": "<PERSON><PERSON><PERSON>", "aiAutoReplyDesc": "<PERSON><PERSON><PERSON> mesej pelanggan automatik pintar", "bookingWalkinDesc": "Pen<PERSON><PERSON><PERSON> janji temu dan barisan di tapak", "bookingOnsiteDesc": "<PERSON><PERSON><PERSON> dan penja<PERSON>lan dalam talian", "contentGeneratorDesc": "<PERSON><PERSON><PERSON><PERSON> dan penjanaan kandungan pintar", "leadGenerationDesc": "<PERSON><PERSON><PERSON> dan pengu<PERSON>an pelanggan berpotensi", "quickCreate": "Cipta Pantas", "dragToCreate": "<PERSON>et jenis ejen ke sini untuk cipta dengan pantas", "orUseSmartWizard": "atau gunakan Wizard <PERSON><PERSON> untuk panduan penciptaan", "noAgentsFound": "Tiada ejen <PERSON>", "adjustFilters": "Cuba laraskan kriteria carian atau penapis anda", "hideCards": "Sembunyikan Kad", "hideQuickCreate": "Sembunyikan Cipta Pantas", "clickCardToCreate": "<PERSON><PERSON> mana-mana kad", "orDragToCreate": "untuk cipta serta-merta, atau seret ke zon lepas di bawah untuk pilihan lanjutan", "clickToCreateInstantly": "Klik untuk cipta serta-merta", "dragToCustomize": "Lanjutan: <PERSON><PERSON> kad untuk sesuaikan sebelum mencipta", "creatingAgent": "Mencipta ejen...", "releaseToCreate": "Lepaskan untuk cipta", "agentWithCustomSettings": "ejen dengan tetapan tersuai", "dropZone": "<PERSON><PERSON>", "dragCardHere": "<PERSON>et mana-mana kad ejen ke sini untuk pilihan penciptaan lanjutan", "openMenu": "Buka menu", "agentCreated": "<PERSON><PERSON><PERSON>", "creationFailed": "<PERSON><PERSON><PERSON><PERSON>", "smartAgentCreated": "<PERSON><PERSON><PERSON><PERSON>", "smartAgentCreatedDesc": "<PERSON><PERSON><PERSON> pintar anda telah dicipta dan dikonfigurasi dengan jayanya.", "creationErrorDesc": "<PERSON><PERSON> berlaku semasa mencipta ejen, sila cuba lagi.", "draftSaved": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "draftSavedDesc": "<PERSON><PERSON><PERSON> anda telah disimpan sebagai draf dan boleh dikonfigu<PERSON>i kem<PERSON>an.", "saveFailed": "Simp<PERSON>", "saveErrorDesc": "<PERSON><PERSON> berlaku semasa <PERSON> dra<PERSON>, sila cuba lagi.", "deleting": "Memadam...", "confirmDeleteButton": "Ya, padam ejen"}, "systemMonitoring": {"title": "Papan P<PERSON> Si<PERSON>", "description": "Pemantauan prestasi dan kesihatan sistem masa nyata", "viewDetailedReport": "<PERSON><PERSON>", "cpuUsage": "Penggunaan CPU", "memoryUsage": "<PERSON><PERSON><PERSON><PERSON>", "storageSpace": "<PERSON><PERSON>", "networkLatency": "<PERSON><PERSON><PERSON>", "currentProcessorUtilization": "<PERSON><PERSON><PERSON><PERSON> pemproses semasa", "systemMemoryConsumption": "Pengg<PERSON>an memori sistem", "diskStorageUtilization": "Penggunaan storan cakera", "averageResponseLatency": "<PERSON><PERSON><PERSON> latensi sambutan", "healthy": "Sihat", "warning": "<PERSON><PERSON>", "critical": "<PERSON><PERSON><PERSON><PERSON>", "systemStatus": "Status Sistem", "allSystemsOperational": "<PERSON><PERSON><PERSON> sistem beroperasi", "serviceStatus": "Status Perkhidmatan", "uptime": "Masa Aktif", "response": "<PERSON><PERSON><PERSON>", "version": "<PERSON><PERSON><PERSON>", "lastCheck": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "justNow": "<PERSON><PERSON> sahaja", "minAgo": "minit lalu", "resourceDistribution": "<PERSON><PERSON><PERSON>", "performanceTrends": "Trend Prestasi 24 Jam", "viewDetailedAnalytics": "<PERSON><PERSON> Analit<PERSON>", "apiGateway": "Pintu Masuk API", "coreService": "Perkhidmatan Teras", "aiIntelligenceService": "Perkhidmatan <PERSON> AI", "clientApplication": "Aplikasi Klien", "cpu": "CPU", "memory": "<PERSON><PERSON><PERSON>", "storage": "Storan", "network": "<PERSON><PERSON><PERSON><PERSON>", "cores": "Teras", "usage": "<PERSON><PERSON><PERSON><PERSON>", "total": "<PERSON><PERSON><PERSON>", "current": "<PERSON><PERSON><PERSON>", "threshold": "Ambang", "port": "Port", "systemLogs": "Log Sistem", "businessAnalytics": "<PERSON><PERSON><PERSON>", "systemResourceUsageOverview": "<PERSON><PERSON><PERSON><PERSON> pengg<PERSON>an sumber sistem", "realTimeSystemPerformanceMonitoring": "Pemantauan prestasi sistem masa nyata", "currentStatusOfAllPlatformServices": "Status semasa semua perkhidmatan platform", "of": "<PERSON><PERSON><PERSON>"}, "dataOverview": {"title": "Ikhtisar Data", "description": "<PERSON><PERSON><PERSON><PERSON> kom<PERSON><PERSON><PERSON><PERSON><PERSON> metrik data utama dan penunjuk prestasi", "totalRevenue": "<PERSON><PERSON><PERSON>", "revenueChange": "+20.1% dari bulan lepas", "activeUsers": "Pengguna Aktif", "userChange": "+180.1% dari bulan lepas", "newOrders": "<PERSON><PERSON><PERSON>", "orderChange": "+19% dari bulan lepas", "systemHealth": "<PERSON><PERSON><PERSON><PERSON>", "uptimeValue": "99.2%", "lastCheck": "2 minit yang lalu", "revenueTrends": "<PERSON><PERSON><PERSON>", "monthlyRevenueOverview": "<PERSON><PERSON><PERSON><PERSON> dan analisis hasil bulanan", "userGrowth": "Pertumbuhan Pengguna", "userSignupsOverTime": "Pendaftaran pengguna dan corak pertum<PERSON>han", "chartPlaceholder": "Carta akan dipaparkan di sini", "dataUpdatedHourly": "Data dikemaskini setiap jam", "realTimeUpdates": "<PERSON><PERSON> kini masa nyata", "recentActivities": "Aktiviti Terkini", "recentActivitiesDescription": "Aktiviti pengguna terkini dan peristiwa sistem", "dataTablePlaceholder": "Jadual data akan dipaparkan di sini", "showingLastEntries": "<PERSON><PERSON><PERSON><PERSON><PERSON> 100 entri terakhir", "viewAll": "<PERSON><PERSON>"}, "marketingCampaigns": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, dan jejaki kempen p<PERSON> anda.", "createNewCampaign": "<PERSON><PERSON><PERSON>", "allCampaigns": "<PERSON><PERSON><PERSON>", "active": "Aktif", "scheduled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "drafts": "Draf", "completed": "Se<PERSON><PERSON>", "archived": "Diark<PERSON>", "noCampaignsMatchFilters": "<PERSON><PERSON><PERSON> kempen sepadan dengan penapis anda", "createFirstCampaign": "<PERSON><PERSON><PERSON> kempen pertama anda untuk bermula.", "tryDifferentFilter": "Cuba penapis berbeza atau cipta kempen baru.", "viewEdit": "Lihat/Edit", "viewReport": "<PERSON><PERSON>", "duplicate": "Pendua", "archive": "Arkib", "delete": "Padam", "targetAudience": "<PERSON><PERSON><PERSON>", "scheduledFor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activeDates": "Tarikh Aktif", "ongoing": "Sedang Be<PERSON>lan", "keyMetrics": "<PERSON><PERSON>", "viewDetails": "<PERSON><PERSON>"}, "settingsLanding": {"title": "Pusat Tetapan", "autoReplyTitle": "Tetapan <PERSON>", "autoReplyDesc": "Konfigurasi peraturan dan tingkah laku balasan automatik.", "goToAutoReply": "Pergi ke Tetapan <PERSON> Auto", "walkinBookingTitle": "Tetapan Tempahan Walk-in", "walkinBookingDesc": "<PERSON><PERSON> pilihan dan konfigu<PERSON>i tempahan walk-in.", "goToWalkinBooking": "<PERSON>gi ke Tetapan Tempahan Walk-in", "onsiteBookingTitle": "Tetapan Tempahan Di Tapak", "onsiteBookingDesc": "<PERSON><PERSON> pilihan dan konfigurasi tempahan di tapak.", "goToOnsiteBooking": "Pergi ke Tetapan Tempahan Di Tapak", "contentGeneratorTitle": "<PERSON><PERSON><PERSON>", "contentGeneratorDesc": "<PERSON>n<PERSON><PERSON><PERSON><PERSON> jenama dan tetapan pen<PERSON>an kandungan.", "goToContentGenerator": "<PERSON><PERSON> ke <PERSON>", "platformApiTitle": "Pengurusan API Platform", "platformApiDesc": "Sambung dan urus integrasi platform pihak ketiga.", "goToPlatformApi": "Pergi ke Pengurusan API Platform"}, "chatPage": {"title": "Pembantu Tempahan Pintar", "subtitle": "Sistem tempahan pintar dan perkhidmatan pelanggan berteknologi AI", "welcomeMessage": "Halo! Saya pembantu tempahan pintar iBuddy. <PERSON>a boleh membantu anda dengan perkhidmatan berikut:\\n\\n🏪 Tempahan Beratur di Lokasi\\n🚗 Tempahan Perkhidmatan di Rumah\\n📋 Semak Status Tempahan\\n⚙️ Tetapan Perkhidmatan\\n\\nSila pilih perkhidmatan yang anda perlukan, atau beritahu saya keperluan anda secara langsung!", "walkinBooking": "<PERSON><PERSON><PERSON> <PERSON>", "walkinDescription": "Sertai barisan perkhidmatan di lokasi", "onsiteBooking": "Perkhidmata<PERSON>", "onsiteDescription": "Jadualkan lawatan juruteknik ke rumah", "checkBooking": "<PERSON><PERSON><PERSON>", "checkDescription": "Lihat status tempahan", "contactSupport": "Hubungi <PERSON>", "supportDescription": "Sokongan pelanggan manusia", "walkinResponse": "Sedang mengatur perkhidmatan beratur di lokasi untuk anda...\\n\\n📍 Sila imbas kod QR atau berikan nombor telefon anda kepada kakitangan selepas tiba\\n⏰ Anggaran masa menunggu semasa: 15 minit\\n👥 Panjang barisan semasa: 3 orang\\n\\nNombor barisan anda ialah: A008", "basicConsultation": "<PERSON><PERSON><PERSON>", "onsiteResponse": "Sedang mengatur tempahan perkhidmatan di rumah untuk anda...\\n\\n📅 Sila pilih masa perkhidmatan\\n📍 Sila berikan alamat terperinci\\n🔧 Perkhidmatan tersedia: Pembaikan peralatan, sokongan teknikal, pemasangan\\n💰 Yuran perkhidmatan: <PERSON><PERSON>ra berdasarkan jarak\\n\\nSila berikan butiran lanjut untuk mengesahkan tempahan.", "checkResponse": "Menyemak maklumat tempahan untuk anda...\\n\\n📋 Tempahan terkini:\\n1. Perkhidmatan di lokasi - Hari ini 14:30 (<PERSON><PERSON><PERSON>)\\n2. Pembaikan di rumah - Esok 10:00 (<PERSON><PERSON><PERSON>)\\n\\nUntuk maklumat terperinci, sila berikan nombor tempahan.", "supportResponse": "Menghubungkan anda dengan perkhidmatan pelanggan manusia...\\n\\n📞 Talian perkhidmatan: ************\\n⏰ Waktu perkhidmatan: 9:00-21:00\\n💬 Perkhidmatan pelanggan dalam talian telah <PERSON>, ejen akan membantu anda sebentar lagi.", "defaultResponse": "<PERSON><PERSON>, saya tidak faham operasi ini. <PERSON>la pilih perkhid<PERSON>n lain atau terangkan keperluan anda secara langsung.", "selectedAction": "Dipi<PERSON>h: {{action}}", "keywordBooking": "te<PERSON><PERSON>", "keywordQueue": "<PERSON><PERSON><PERSON>", "keywordOnsite": "di rumah", "keywordRepair": "<PERSON>em<PERSON><PERSON><PERSON>", "keywordCheck": "semak", "keywordStatus": "status", "keywordCancel": "batal", "keywordSupport": "sokongan", "keywordHuman": "manusia", "onsiteScheduleResponse": "<PERSON>a akan mengatur tempahan perkhidmatan di rumah untuk anda.\\n\\nSila beritahu saya:\\n1. Perkhidmatan apa yang anda perlukan? (p<PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON>, per<PERSON><PERSON><PERSON>, dll.)\\n2. Masa perkhidmatan yang diingini\\n3. <PERSON><PERSON><PERSON> perkhidmatan\\n\\nAtau anda boleh klik butang 'Tempahan di Rumah' untuk tempahan pantas.", "walkinScheduleResponse": "<PERSON>a akan mengatur perkhidmatan beratur di lokasi untuk anda.\\n\\nMaklumat perkhidmatan semasa:\\n📍 Lokasi perkhidmatan: <PERSON><PERSON> XXX\\n⏰ Waktu perniagaan: 9:00-18:00\\n👥 Barisan semasa: 3 orang\\n⌛ Anggaran menunggu: 15 minit\\n\\nKlik butang 'Beratur di Lokasi' untuk menyertai barisan.", "statusCheckResponse": "Sila berikan nombor tempahan atau nombor telefon berdaftar anda, saya akan semak status tempahan anda.\\n\\nAnda juga boleh klik butang 'Semak Tempahan' untuk melihat semua rekod tempahan anda.", "cancelResponse": "Saya akan membantu anda membatalkan tempahan. Sila berikan:\\n1. Nombor tempahan\\n2. Nombor telefon berdaftar\\n\\nSaya akan memproses pembatalan selepas mengesahkan maklumat.", "humanSupportResponse": "Mengh<PERSON>ungkan anda dengan perkhidmatan pelanggan manusia, sila tunggu...\\n\\n💬 Perkhidmatan pelanggan akan menghubungi anda dalam 1-2 minit\\n📞 Untuk perkhidmatan kecemasan, sila hubungi ************ secara langsung", "generalResponse": "<PERSON><PERSON><PERSON> mesej anda: '{{message}}'.\\n\\nSaya boleh membantu anda dengan:\\n• Tempahan beratur di lokasi\\n• Tempahan perkhidmatan di rumah\\n• Semak status tempahan\\n• Ubah suai atau batal tempahan\\n\\nSila pilih perkhidmatan yang anda perlukan, atau beritahu saya keperluan khusus anda.", "serviceType": "<PERSON><PERSON>", "time": "<PERSON><PERSON>", "address": "<PERSON><PERSON><PERSON>", "smartChat": "Sembang Pintar", "bookingService": "Perkhidmatan Tempahan", "serviceStatus": "Status Perkhidmatan", "assistantName": "Pembantu Pintar iBuddy", "online": "<PERSON><PERSON>", "inputPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mesej anda..."}, "homePage": {"nav": {"home": "<PERSON><PERSON>", "features": "Ciri-ciri", "pricing": "<PERSON><PERSON>", "about": "Tentang", "contact": "Hubung<PERSON>"}, "hero": {"title": "Platform Penciptaan Kandungan Berkuasa AI", "subtitleRegular": "<PERSON><PERSON> idea anda menjadi ", "subtitleGradient": "pengalaman digital yang indah", "description": "Ubah proses pencipta<PERSON> kandungan anda dengan platform AI canggih kami. <PERSON>, opti<PERSON><PERSON>, dan ed<PERSON>an kandungan menarik merentasi berbilang saluran dalam beberapa minit, bukan jam.", "cta": "<PERSON><PERSON>", "dynamicSubtitle": "Alami masa depan reka bentuk interaktif dengan penjanaan corak dinamik", "explorePatterns": "Terokai Corak", "currentPattern": "<PERSON>k <PERSON>: "}, "features": {"title": "Ciri Berkuasa untuk Penciptaan Kandungan Moden", "description": "<PERSON><PERSON> yang anda perlukan untuk mencipta, men<PERSON><PERSON><PERSON><PERSON><PERSON>, dan menged<PERSON>an kandungan yang menarik audiens anda dan mendorong keputusan.", "items": {"contentGeneration": {"title": "<PERSON><PERSON><PERSON>", "description": "Algoritma AI canggih untuk mencipta kandungan berkualiti tinggi dan menarik yang bergema dengan audiens anda."}, "easeOfUse": {"title": "<PERSON><PERSON>", "description": "<PERSON><PERSON> muka intuitif yang direka untuk pencipta dari semua tahap kema<PERSON>. <PERSON><PERSON> mencipta kandungan menakjubkan dalam beberapa minit."}, "flexiblePricing": {"title": "<PERSON><PERSON>", "description": "<PERSON>elan mampu milik yang berkembang dengan k<PERSON>an anda. <PERSON><PERSON><PERSON>, naik taraf a<PERSON> anda berse<PERSON>."}, "cloudBased": {"title": "Platform Berasaskan Awan", "description": "<PERSON>ks<PERSON> alat penciptaan kandungan anda di mana-mana, bila-bila masa dengan jaminan masa aktif 99.9%."}, "multiPlatform": {"title": "Pengedaran Multi-platform", "description": "Edarkan kandungan anda dengan lancar merentasi berbilang platform dengan penerbitan satu klik."}, "support": {"title": "Sokongan Pelanggan 24/7", "description": "Pasukan sokongan khusus kami sentiasa di sini untuk membantu anda berjaya dengan strategi kandungan anda."}, "optimization": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Pengoptimuman kandungan pintar berdasarkan analitik prestasi dan wawasan audiens."}, "builtWithLove": {"title": "<PERSON><PERSON>", "description": "Dibuat oleh pencipta kandungan, untuk pencipta kandungan. <PERSON><PERSON> faham k<PERSON> anda."}}}, "integrations": {"title": "Integrasi Platform Berkuasa", "description": "Sambung dengan platform dan alat kegemaran anda untuk mencipta aliran kerja automatik yang lancar", "footer": {"title": "Pengalaman Integrasi <PERSON>", "description": "Sambung dengan beratus-ratus aplikasi dan perkhidmatan pihak ketiga melalui integrasi Webhook dan API, menjadikan proses per<PERSON> anda lebih pintar dan automatik."}}, "testimonials": {"title": {"whatOur": "Apa Kata", "customersSay": "<PERSON><PERSON>ng<PERSON>"}, "subtitle": "Sertai ribuan pelanggan yang berpuas hati yang telah mengubah proses penciptaan kandungan mereka. Berdasarkan {{count}} ulasan dengan penilaian purata {{rating}} daripada 5 bintang.", "reviews": [{"text": "iTeraBiz mengubah proses penciptaan kandungan kami. Ka<PERSON> kini mengh<PERSON>an 3x lebih kandungan dalam separuh masa.", "author": {"name": "<PERSON>", "title": "<PERSON><PERSON><PERSON>"}}, {"text": "Wawasan AI sangat menakjubkan. <PERSON><PERSON> penglibatan kami meningkat 400% sejak menggunakan iTeraBiz.", "author": {"name": "<PERSON>", "title": "Ketua Pegawai Eksekutif"}}, {"text": "Pelaburan terbaik yang kami buat tahun ini. ROI sangat mengagumkan dan pasukan sokongan sangat hebat.", "author": {"name": "<PERSON>", "title": "<PERSON><PERSON><PERSON>"}}, {"text": "Platform luar biasa yang merevolusikan cara kami mendekati strategi dan pelaks<PERSON>an kandungan.", "author": {"name": "<PERSON>", "title": "<PERSON><PERSON>"}}, {"text": "Ciri automasi telah menjimatkan berjam-jam masa kami sambil meningkatkan kualiti kandungan kami.", "author": {"name": "<PERSON>", "title": "Pengarah Digital"}}, {"text": "Keputusan luar biasa! Produktiviti pasukan kami meningkat 300% dengan pelaksanaan iTeraBiz.", "author": {"name": "<PERSON>", "title": "<PERSON><PERSON><PERSON>"}}], "stats": {"average": "purata {{value}}", "reviews": "{{count}}+ ul<PERSON>n", "satisfaction": "95% kepuasan"}}, "pricing": {"title": "<PERSON><PERSON><PERSON>", "description": "Kembangkan penciptaan kandungan anda dengan platform berkuasa AI kami\nSemua pelan termasuk akses kepada alat AI canggih, analitik, dan sokongan khusus kami.", "monthly": "Bulanan", "annualBilling": "<PERSON><PERSON>", "save20": "Jimat 20%", "plans": {"starter": {"name": "PEMULA", "price": "29", "yearlyPrice": "23", "period": "sebulan", "description": "Sempurna untuk individu dan pencipta kandungan kecil", "buttonText": "<PERSON><PERSON>", "features": ["10,000 perkataan jana AI/bulan", "5 templat kandungan", "<PERSON><PERSON><PERSON>", "Sokongan emel", "1 akaun pengguna", "Kualiti kandungan standard"]}, "professional": {"name": "PROFESIONAL", "price": "79", "yearlyPrice": "63", "period": "sebulan", "description": "Ideal untuk pasukan yang berkembang dan agensi kandungan", "buttonText": "<PERSON><PERSON>", "features": ["50,000 perkataan jana AI/bulan", "25+ templat kandungan", "Analitik <PERSON> & wa<PERSON>an", "Sokongan keutamaan", "5 akaun pengguna", "Akses API", "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "Penerbitan multi-platform"]}, "enterprise": {"name": "PERUSAHAAN", "price": "199", "yearlyPrice": "159", "period": "sebulan", "description": "Untuk organisasi besar dengan k<PERSON>", "buttonText": "Hubungi Ju<PERSON>", "features": ["<PERSON><PERSON><PERSON><PERSON> jana <PERSON> had", "Templat & aliran kerja tersuai", "<PERSON><PERSON><PERSON> aka<PERSON> k<PERSON>us", "Penyelesaian label putih", "<PERSON><PERSON><PERSON> tanpa had", "API lanjutan & integrasi", "Jaminan SLA", "Latihan model AI tersuai", "Permintaan ciri keutamaan"]}}}, "cta": {"title": "Bersedia untuk Mengubah?", "subtitle": "Sertai ribuan pencipta yang mempercayai iTeraBiz untuk keperluan kandungan mereka", "buttonText": "<PERSON><PERSON><PERSON>", "ctaText": "<PERSON><PERSON><PERSON> →", "transformTitle": "Bersedia untuk Mengubah Penciptaan Kandungan Anda?", "transformSubtitle": "Sertai ribuan pencipta kandungan yang sudah menggunakan AI untuk mencipta kandungan menakjubkan. <PERSON>lakan percubaan percuma anda hari ini.", "startFreeTrial": "<PERSON><PERSON>", "contactSales": "Hubungi Ju<PERSON>", "watchDemo": "<PERSON><PERSON>"}, "footer": {"followUs": "<PERSON><PERSON><PERSON> kami", "copyright": "© 2025 iTeraBiz. Hak cipta terpelihara.", "brand": "iTeraBiz", "logoAlt": "Logo iTeraBiz", "socialLinks": {"facebook": "<PERSON><PERSON><PERSON> kami di Facebook", "twitter": "<PERSON><PERSON><PERSON> kami di Twitter", "linkedin": "<PERSON><PERSON><PERSON> kami di LinkedIn"}}}}