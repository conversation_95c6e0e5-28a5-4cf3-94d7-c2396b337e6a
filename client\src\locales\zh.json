{"staff": {"management": "员工管理", "overview": "员工概览", "description": "管理整个组织的员工成员、部门、角色和权限。", "addStaffMember": "添加员工", "searchStaff": "搜索员工姓名或邮箱...", "allRoles": "所有角色", "allDepartments": "所有部门", "allStatus": "所有状态", "name": "姓名", "email": "邮箱", "role": "角色", "department": "部门", "status": "状态", "joinDate": "入职日期", "actions": "操作", "active": "活跃", "inactive": "不活跃", "pending": "待定", "edit": "编辑", "delete": "删除", "confirm": "确认", "cancel": "取消", "filterByRole": "按角色筛选", "filterByStatus": "按状态筛选", "noStaffMatchFilters": "未找到符合条件的员工", "adjustFilters": "调整搜索条件或筛选器，或添加新员工。", "invited": "已邀请", "memberSavedSuccess": "员工 {{firstName}} {{lastName}} 已{{action}}。", "memberSaveError": "{{action}}员工失败，请重试。", "currentlyStaffMembers": "当前 {{count}} 名员工", "activateAccount": "激活账户", "confirmDeleteMember": "您确定要删除员工 \"{{name}}\"？此操作无法撤销。"}, "departments": {"management": "部门管理", "description": "管理组织架构和部门配置", "addDepartment": "添加部门", "editDepartment": "编辑部门", "createDepartment": "创建部门", "updateDepartment": "更新部门", "deleteDepartment": "删除部门", "totalDepartments": "总部门数", "totalEmployees": "总员工数", "totalBudget": "总预算", "avgTeamSize": "平均团队规模", "departmentName": "部门名称", "manager": "经理", "location": "位置", "phone": "电话", "budget": "预算", "employeeCount": "员工数量", "createdAt": "创建时间", "searchDepartments": "搜索部门、经理或描述...", "filterByStatus": "按状态筛选", "addNewDepartment": "添加新部门", "createNewDepartment": "为您的组织创建新部门", "updateDepartmentInfo": "更新部门信息和设置", "briefDescription": "部门职责的简要描述", "departmentHead": "部门负责人", "annualBudget": "年度预算", "physicalLocation": "物理位置或办公室", "acrossAllDepartments": "跨所有部门", "employeesPerDepartment": "每个部门的员工数", "manageDepartmentStructure": "管理您组织的部门结构和分配", "noDepartmentsFound": "未找到部门", "adjustSearchCriteria": "尝试调整您的搜索条件", "addFirstDepartment": "添加您的第一个部门开始", "departmentCreatedSuccess": "部门创建成功", "departmentUpdatedSuccess": "部门信息更新成功", "departmentDeletedSuccess": "部门删除成功", "departmentStatusChanged": "部门状态已更改", "fillRequiredFields": "请填写必填字段", "confirmDeleteDepartment": "您确定要删除此部门吗？此操作无法撤销，会影响员工。", "employees": "名员工", "annualBudgetAllocation": "年度预算分配", "notSpecified": "未指定", "humanResources": "人力资源", "engineering": "工程开发", "marketing": "市场营销", "sales": "销售", "finance": "财务", "humanResourcesDesc": "管理人事、政策和员工关系", "engineeringDesc": "软件开发和技术运营", "marketingDesc": "品牌推广和客户获取", "salesDesc": "收入生成和客户关系", "financeDesc": "财务规划和会计操作", "buildingAFloor1": "A楼1层", "buildingAFloor2": "A楼2层", "buildingAFloor3": "A楼3层", "buildingAFloor4": "A楼4层", "buildingBFloor24": "B楼2-4层", "customerService": "客户服务", "technicalSupport": "技术支持", "emailPlaceholder": "部门@公司.com", "phonePlaceholder": "+86 138-0000-0000"}, "roles": {"management": "角色与权限", "description": "管理系统角色和访问权限", "addRole": "添加角色", "editRole": "编辑角色", "createRole": "创建角色", "updateRole": "更新角色", "deleteRole": "删除角色", "totalRoles": "总角色数", "totalUsers": "总用户数", "totalPermissions": "总权限数", "customRoles": "自定义角色", "roleName": "角色名称", "roleDescription": "角色描述", "permissions": "权限", "users": "用户", "type": "类型", "level": "级别", "system": "系统", "custom": "自定义", "admin": "系统管理员", "manager": "经理", "user": "用户", "viewer": "查看者", "editor": "内容编辑者", "agent": "代理人", "roles": "角色", "userAssignments": "用户分配", "systemRoles": "系统角色", "manageRolesPermissions": "管理整个系统的角色及其权限", "systemPermissions": "系统权限", "overviewAllPermissions": "系统中所有可用权限的概览", "userRoleAssignments": "用户角色分配", "manageUserRoleAssignments": "管理个人用户的角色分配", "currentRole": "当前角色", "changeRole": "更改角色", "createNewRole": "创建具有特定权限的新角色", "updateRoleInfo": "更新角色信息和权限", "briefRoleDescription": "角色职责的简要描述", "withAssignedRoles": "分配了角色", "acrossCategories": "跨{{count}}个类别", "createdByUsers": "由用户创建", "searchRoles": "搜索角色...", "noRolesFound": "未找到角色", "createFirstCustomRole": "创建您的第一个自定义角色", "roleCreatedSuccess": "角色创建成功", "roleUpdatedSuccess": "角色更新成功", "roleDeletedSuccess": "角色删除成功", "cannotDeleteSystemRole": "无法删除系统角色", "confirmDeleteRole": "您确定要删除此角色吗？此操作无法撤销，会影响用户。", "fillRoleNameDescription": "请填写角色名称和描述", "role": "角色", "roleActions": "角色操作", "modifyPermissions": "修改权限"}, "permissions": {"userManagement": "用户管理", "contentManagement": "内容管理", "analytics": "分析", "systemSettings": "系统设置", "financial": "财务", "apiAccess": "API访问", "createUsers": "创建用户", "viewUsers": "查看用户", "editUsers": "编辑用户", "deleteUsers": "删除用户", "createContent": "创建内容", "viewContent": "查看内容", "editContent": "编辑内容", "deleteContent": "删除内容", "publishContent": "发布内容", "viewAnalytics": "查看分析", "exportAnalytics": "导出分析", "viewSettings": "查看设置", "manageSettings": "管理设置", "securitySettings": "安全设置", "viewBilling": "查看账单", "manageBilling": "管理账单", "apiReadAccess": "API读取访问", "apiWriteAccess": "API写入访问", "apiAdminAccess": "API管理员访问", "fullSystemAccess": "拥有所有权限的完整系统访问权限", "departmentManagement": "部门管理和用户监督", "contentCreation": "内容创建和管理", "readOnlyAccess": "对大多数内容的只读访问权限"}, "header": {"notifications": "通知", "profile": "个人资料", "logOut": "登出", "home": "主页", "newFeatureReleased": "新功能发布！", "checkLatestUpdates": "查看我们平台的最新更新。", "systemMaintenance": "系统维护", "scheduledMaintenance": "计划于明天凌晨2点进行。", "viewAllNotifications": "查看所有通知"}, "homePage": {"nav": {"home": "首页", "features": "功能", "pricing": "价格", "about": "关于", "contact": "联系我们"}, "hero": {"title": "人工智能内容创作平台", "subtitleRegular": "将您的想法转化为", "subtitleGradient": "美丽的数字体验", "description": "使用我们先进的人工智能平台，改变您的内容创作流程。在几分钟内生成、优化和分发引人入胜的内容到多个渠道，而不是几小时。", "cta": "免费开始", "dynamicSubtitle": "通过动态模式生成，体验交互式设计的未来", "explorePatterns": "探索模式", "currentPattern": "当前模式: "}, "features": {"title": "为现代内容创作提供强大功能", "description": "您需要的一切，用于创建、优化和分发能够吸引观众并带来成果的内容。", "items": {"contentGeneration": {"title": "AI驱动的内容生成", "description": "先进的AI算法，创造能与您的受众产生共鸣的高质量、引人入胜的内容。"}, "easeOfUse": {"title": "易于使用", "description": "直观的界面，专为各种技能水平的创作者设计。几分钟内即可开始创作精彩内容。"}, "flexiblePricing": {"title": "灵活的定价", "description": "根据您的需求扩展的实惠计划。免费开始，随时准备升级。"}, "cloudBased": {"title": "基于云的平台", "description": "随时随地访问您的内容创作工具，保证99.9%的正常运行时间。"}, "multiPlatform": {"title": "多平台分发", "description": "通过一键发布，无缝地将您的内容分发到多个平台。"}, "support": {"title": "24/7客户支持", "description": "我们专业的支持团队随时在这里帮助您成功实施内容策略。"}, "optimization": {"title": "智能优化", "description": "基于性能分析和受众洞察的智能内容优化。"}, "builtWithLove": {"title": "用爱构建", "description": "由内容创作者为内容创作者精心打造。我们了解您的需求。"}}}, "integrations": {"title": "强大的平台集成", "description": "连接您最喜欢的平台和工具，创建无缝的自动化工作流程", "footer": {"title": "无缝的集成体验", "description": "通过Webhook和API集成，与数百个第三方应用程序和服务连接，使您的业务流程更加智能和自动化。"}}, "testimonials": {"title": {"whatOur": "我们的", "customersSay": "客户怎么说"}, "subtitle": "加入数千名满意的客户，他们已经改变了内容创作流程。基于{{count}}条评论，平均评分为{{rating}}星（满分5星）。", "reviews": [{"text": "iTeraBiz改变了我们的内容创作流程。我们现在用一半的时间制作三倍的内容。", "author": {"name": "莎拉·约翰逊", "title": "市场总监"}}, {"text": "AI的洞察力令人难以置信。使用iTeraBiz后，我们的参与率提高了400%。", "author": {"name": "迈克尔·陈", "title": "首席执行官"}}, {"text": "这是我们今年最好的投资。投资回报率非常惊人，支持团队也很棒。", "author": {"name": "艾米丽·罗德里格斯", "title": "内容经理"}}, {"text": "卓越的平台，彻底改变了我们制定内容策略和执行的方式。", "author": {"name": "大卫·金", "title": "市场副总裁"}}, {"text": "自动化功能为我们节省了无数小时，同时提高了我们的内容质量。", "author": {"name": "丽萨·王", "title": "数字总监"}}, {"text": "成果斐然！实施iTeraBiz后，我们团队的生产力提高了300%。", "author": {"name": "罗伯特·史密斯", "title": "运营经理"}}], "stats": {"average": "平均 {{value}} 分", "reviews": "{{count}}+ 条评论", "satisfaction": "95% 满意度"}}, "pricing": {"title": "选择您的完美计划", "description": "使用我们的人工智能平台扩展您的内容创作\n所有计划都包括访问我们先进的AI工具、分析和专属支持。", "plans": {"starter": {"name": "入门版", "price": "29", "yearlyPrice": "23", "period": "每月", "description": "非常适合个人和小型内容创作者", "buttonText": "开始免费试用", "features": ["每月10,000个AI生成单词", "5个内容模板", "基础分析", "邮件支持", "1个用户账户", "标准内容质量"]}, "professional": {"name": "专业版", "price": "79", "yearlyPrice": "63", "period": "每月", "description": "适合成长中的团队和内容代理机构", "buttonText": "开始使用", "features": ["每月50,000个AI生成单词", "25+个内容模板", "高级分析与洞察", "优先支持", "5个用户账户", "API访问", "自定义品牌", "多平台发布"]}, "enterprise": {"name": "企业版", "price": "199", "yearlyPrice": "159", "period": "每月", "description": "适用于有企业需求的大型组织", "buttonText": "联系销售", "features": ["无限AI生成内容", "自定义模板与工作流", "专属客户经理", "白标解决方案", "无限用户", "高级API与集成", "SLA保证", "自定义AI模型训练", "优先功能请求"]}}}, "cta": {"title": "准备好改变了吗？", "subtitle": "加入成千上万信任iTeraBiz满足其内容需求的创作者", "buttonText": "开始您的旅程", "ctaText": "开始您的旅程 →", "transformTitle": "准备好改变您的内容创作了吗？", "transformSubtitle": "加入成千上万已经在使用AI创造惊人内容的内容创作者。立即开始您的免费试用。", "startFreeTrial": "开始免费试用", "contactSales": "联系销售", "watchDemo": "观看演示"}, "footer": {"followUs": "关注我们", "copyright": "© 2025 iTeraBiz. 保留所有权利。", "brand": "iTeraBiz", "logoAlt": "iTeraBiz标志", "socialLinks": {"facebook": "在Facebook上关注我们", "twitter": "在Twitter上关注我们", "linkedin": "在LinkedIn上关注我们"}}}, "overview": {"title": "业务概览", "description": "欢迎回来！快速了解您的业务状况和关键指标", "viewDetailedAnalysis": "查看详细分析", "coreBusinessIndicators": "核心业务指标", "viewTrendAnalysis": "查看趋势分析", "quickNavigation": "快速导航", "selectFunctionModule": "选择您要查看的功能模块", "totalUsers": "总用户数", "activeUsers": "活跃用户", "conversionRate": "转化率", "monthlyGrowth": "月度增长", "dailyActiveUsers": "日活跃用户", "monthlyImprovement": "月度改善", "systemMonitoring": "系统监控", "systemMonitoringDesc": "实时系统状态和性能监控", "businessAnalytics": "业务分析", "businessAnalyticsDesc": "业务模块的使用分析和趋势洞察", "dataInsights": "数据洞察", "dataInsightsDesc": "AI驱动的代理和用户行为深度数据洞察", "aiAgentManagement": "AI代理管理", "aiAgentManagementDesc": "创建和管理AI代理", "bookingSystem": "预订系统", "bookingSystemDesc": "预约管理和服务调度", "platformIntegration": "平台集成", "platformIntegrationDesc": "第三方平台API集成管理", "live": "实时", "insights": "洞察", "ai": "AI", "management": "管理", "service": "服务", "integration": "集成", "uptime": "99.9% 正常运行时间", "moduleAnalysis": "4个模块分析", "analysisDimensions": "7个分析维度", "activeAgents": "6个活跃代理", "appointments": "156个预约", "platforms": "3个平台", "todaysOverview": "今日概况", "todaysVisits": "今日访问量", "aiInteraction": "AI交互次数", "newAppointment": "新预约", "newLead": "新潜在客户"}, "navigation": {"overview": "概览", "dataInsight": "数据洞察", "agents": "代理", "platformApi": "平台API", "billing": "账单", "analytics": "分析", "staff": "员工", "systemSettings": "系统设置", "profile": "个人资料", "chat": "聊天", "booking": "预订", "settings": "设置", "dashboard": "仪表板", "dataInsights": "数据洞察", "userAnalytics": "用户分析", "businessAnalytics": "业务分析", "contentData": "内容数据", "modules": "模块", "aiAutoReply": "AI自动回复", "walkinBooking": "现场预订", "onsiteBooking": "现场预订", "contentGenerator": "内容生成器", "integrations": "集成", "staffManagement": "员工管理", "general": "常规", "account": "账户", "branding": "品牌", "moduleConfig": "模块配置", "notifications": "通知"}, "common": {"error": "错误", "warning": "警告", "info": "信息", "success": "成功", "configurationRequired": "需要配置", "close": "关闭", "cancel": "取消", "confirm": "确认", "retry": "重试", "save": "保存", "delete": "删除", "edit": "编辑", "view": "查看", "loading": "加载中...", "steps": "步骤", "errorCode": "错误代码", "viewDocumentation": "查看文档", "copy": "复制", "copied": "已复制！", "required": "必填", "next": "下一步", "previous": "上一步", "optional": "可选"}, "settings": {"manageGlobalSettings": "管理和配置系统的全局设置。", "basicSettings": "基本设置", "basicSettingsDesc": "配置系统的基本信息和偏好设置。", "systemName": "系统名称", "systemDescription": "系统描述", "timezone": "时区", "selectTimezone": "选择时区", "dateFormat": "日期格式", "selectDateFormat": "选择日期格式", "logoUrl": "Logo URL", "logoRecommendedSize": "推荐尺寸：200px x 50px", "languageSettings": "语言设置", "languageSettingsDesc": "选择应用程序的界面语言。", "interfaceLanguage": "界面语言", "languageChangeNote": "语言更改将立即在整个应用程序中生效。", "security": "安全性", "dataManagement": "数据管理", "settingsSaved": "您的系统设置已成功更新。", "settingsReset": "设置重置", "settingsResetToDefault": "设置已重置为默认值。", "notificationsDesc": "配置系统如何发送通知及其频率。", "emailNotificationsDesc": "通过电子邮件接收系统更新和提醒。", "smsNotifications": "短信通知", "smsNotificationsDesc": "通过短信接收重要更新和提醒。", "appNotifications": "应用内通知", "appNotificationsDesc": "在应用内接收实时通知和更新。", "note": "注意", "securitySettingsNote": "更改安全设置可能会影响当前已登录的用户。请谨慎操作。", "securitySettings": "安全设置", "securitySettingsDesc": "配置系统的安全选项和策略。", "twoFactorAuth": "两因素认证", "twoFactorAuthDesc": "要求用户通过两个步骤验证其登录。", "passwordExpiryTime": "密码到期时间", "selectPasswordExpiry": "选择密码到期时间", "days": "{{count}}天", "never": "永不", "sessionTimeoutTime": "会话超时时间", "selectSessionTimeout": "选择会话超时时间", "minutes": "{{count}}分钟", "hour": "{{count}}小时", "hours": "{{count}}小时", "maximumLoginAttempts": "最大登录尝试次数", "dataRetentionAndBackup": "数据保留和备份", "dataRetentionDesc": "配置数据保留策略和自动备份设置。", "dataRetentionPeriod": "数据保留期", "selectDataRetention": "选择数据保留期", "months": "{{count}}个月", "year": "{{count}}年", "permanentStorage": "永久存储", "backupFrequency": "备份频率", "selectBackupFrequency": "选择备份频率", "hourly": "每小时", "daily": "每天", "weekly": "每周", "monthly": "每月", "autoDeleteOldBackups": "自动删除旧备份", "autoDeleteDesc": "自动删除超过30天的备份文件。", "backupStorageLocation": "备份存储位置", "selectBackupLocation": "选择备份存储位置", "localServer": "本地服务器", "cloudStorage": "云存储", "both": "两者都是", "dataImportExport": "数据导入/导出", "dataImportExportDesc": "导入或导出系统数据和配置。", "exportSystemConfig": "导出系统配置", "exportDatabase": "导出数据库", "importConfiguration": "导入配置", "notificationTemplates": "通知模板", "notificationTemplatesDesc": "自定义发送给用户的通知模板。", "notificationTemplatesUnderDev": "通知模板功能正在开发中", "reset": "重置", "saveSettings": "保存设置"}, "dataInsight": {"title": "数据洞察仪表板", "description": "AI驱动的分析和智能洞察", "exportReport": "导出报告", "aiInsights": "AI洞察", "userBehavior": "用户行为", "agents": "代理", "leads": "线索", "bookings": "预订", "advanced": "高级", "userEngagementChange": "用户参与模式变化", "userEngagementChangeDesc": "检测到过去一周用户会话持续时间增加了23%，特别是在晚间时段（下午6-9点）。", "leadQualityTrend": "线索质量改善趋势", "leadQualityTrendDesc": "AI模型预测基于当前优化模式，线索质量在未来30天内将提高18%。", "churnRiskAlert": "客户流失风险警报", "churnRiskAlertDesc": "识别出15个用户基于活动减少和参与模式显示早期流失指标。", "highConfidence": "高置信度", "mediumConfidence": "中等置信度", "conversionRateIncrease": "转化率可能增加15%", "increaseMarketingActivities": "考虑在高峰参与时段增加营销活动", "salesConversionIncrease": "预期销售转化增加12%", "prepareSalesTeam": "为增加的高质量线索量准备销售团队", "potentialRevenueLoss": "潜在收入损失$12,000", "implementRetentionCampaigns": "为风险用户实施有针对性的保留活动", "userBehaviorPrediction": "用户行为预测", "leadScoringModel": "线索评分模型", "contentOptimization": "内容优化", "customerRetention": "客户保留", "hoursAgo": "{{count}}小时前", "dayAgo": "{{count}}天前", "identifiedHighRiskUsers": "识别出{{count}}个高风险用户", "bookingIncreaseExpected": "预期下周预订增加12%", "contentPerformanceImprovement": "内容性能将提高15%", "retentionRateExpected": "保留率预期达到92%", "potentialImpact": "潜在影响", "behaviorAnalysis": "行为分析", "trendPrediction": "趋势预测", "riskIdentification": "风险识别", "activeModels": "活跃模型", "aiAnalysisActive": "AI分析活跃", "averageAccuracy": "平均准确率", "averageModelAccuracy": "所有AI系统的平均模型准确率", "predictionsPerHour": "每小时预测", "lastWeekComparison": "与上周对比", "realTimePredictionProcessing": "实时预测处理", "exportingReport": "正在导出全面的AI洞察报告...", "customerSupport": "客户支持", "salesAssistant": "销售助手", "technicalSupport": "技术支持", "leadQualification": "线索资格", "customerLifetimeValue": "客户终身价值", "churnPrediction": "流失预测", "demandForecasting": "需求预测", "priceOptimization": "价格优化", "nextSixMonthsRevenue": "未来6个月收入: $2.4M", "highRiskCustomersIdentified": "识别出{{count}}个高风险客户", "increaseExpectedQ4": "第四季度预期增长35%", "optimalPriceRange": "最佳价格区间: $45-$65", "jan2024": "2024年1月", "feb2024": "2024年2月", "mar2024": "2024年3月", "apr2024": "2024年4月", "may2024": "2024年5月", "jun2024": "2024年6月", "awareness": "认知", "interest": "兴趣", "consideration": "考虑", "intent": "意向", "purchase": "购买", "retention": "保留", "enterprise": "企业", "smb": "中小企业", "startup": "初创企业", "individual": "个人", "trafficSpike": "流量激增", "low": "低", "medium": "中", "high": "高", "unusualTrafficIncrease": "检测到异常流量增加(比基准线高45%)", "monitorServerCapacity": "监控服务器容量", "conversionDrop": "转化率下降", "conversionRateDropped": "移动端用户转化率下降12%", "reviewMobileCheckout": "检查移动端结账流程", "revenueAnomaly": "收入异常", "revenueBelowForecast": "企业部门收入低于预测23%", "investigateEnterprisePipeline": "调查企业销售渠道", "productMarketFitScore": "产品市场匹配度", "customerHealthScore": "客户健康度", "featureAdoptionRate": "功能采用率", "supportTicketResolution": "支持工单解决率", "keyBehaviorMetrics": "关键行为指标和模式", "pageViews": "页面浏览量", "avgSession": "平均会话时长", "conversionRate": "转化率", "bookingTrendsAnalysis": "预订趋势分析", "monthlyBookingPerformance": "月度预订表现和趋势", "completed": "已完成", "bookingPerformanceMetrics": "预订性能指标", "keyPerformanceIndicators": "预订系统关键性能指标", "predictiveModels": "预测模型", "activeModelsCount": "活跃模型数", "avgAccuracy": "平均准确率: 90.8%", "cohortAnalysis": "同期群分析", "activeCohorts": "活跃同期群", "avgRetention": "平均留存率: 72%", "funnelAnalysis": "漏斗分析", "endToEndRate": "端到端转化率", "vsLastMonth": "与上月相比 +0.3%", "anomalyDetection": "异常检测", "activeAlerts": "活跃警报", "highSeverity": "1个高严重性", "predictiveModelsPerformance": "预测模型性能", "modelAccuracyLevels": "AI模型准确率和预测置信度", "cohortRetentionAnalysis": "同期群留存分析", "userRetentionPatterns": "不同同期群的用户留存模式", "conversionFunnelAnalysis": "转化漏斗分析", "userJourneyConversion": "用户旅程各阶段转化", "customerSegmentAnalysis": "客户细分分析", "revenueDistribution": "各客户细分的收入分布", "realTimeAnomalyAlerts": "实时异常警报和建议", "customMetricsDashboard": "自定义指标仪表板", "keyBusinessMetrics": "关键业务指标跟踪", "week1": "第1周", "week2": "第2周", "week3": "第3周", "month2": "第2个月", "month3": "第3个月", "target": "目标", "onTarget": "✓ 达标", "belowTarget": "△ 未达标", "leadSourceAnalysis": "线索来源分析", "leadSourcePerformance": "线索来源性能和质量分析"}, "platformApi": {"title": "平台API集成中心", "description": "统一管理您的社交媒体、电商平台和通讯工具API集成。实时监控连接状态，简化多平台运营。", "searchIntegrations": "搜索平台集成...", "connected": "已连接", "connect": "立即连接", "addNewIntegration": "添加新平台", "discoverMore": "发现更多平台", "webhookConfiguration": "Webhook配置对话框（待实现）", "manageSettings": "管理设置", "disconnect": "断开连接", "resolveIssues": "解决问题", "confirmDisconnect": "确认断开连接", "disconnectSuccess": "断开连接成功", "operationFailed": "操作失败", "discoverModal": "打开探索/添加新集成模态框（待实现）", "refreshFailed": "刷新失败", "loadingPlatforms": "正在加载平台信息...", "browseAndConnect": "浏览并连接更多平台，扩展您的数字化业务版图。", "foundIntegrations": "找到", "integrationsMatching": "个匹配的集成", "all": "全部平台", "productivity": "效率工具", "social": "社交媒体", "communication": "即时通讯", "ecommerce": "电商平台", "customWebhooks": "自定义Webhook", "webhookDescription": "配置自定义webhook端点，实时接收业务事件通知", "facebookPages": "Facebook 企业主页", "facebookPagesDesc": "连接Facebook企业主页，管理帖子发布和客户互动消息", "instagramBusiness": "Instagram 商业账户", "instagramBusinessDesc": "集成Instagram商业功能，自动化内容管理和客户服务", "whatsappBusiness": "WhatsApp 商业API", "whatsappBusinessDesc": "通过WhatsApp Business API与客户建立直接沟通渠道", "facebookMessenger": "Messenger 客服系统", "facebookMessengerDesc": "集成Facebook Messenger，提供智能客服和自动回复功能", "lazada": "Lazada 商家中心", "lazadaDesc": "连接Lazada店铺，自动化订单管理和客户服务流程", "shopee": "Shopee 卖家助手", "shopeeDesc": "集成Shopee商家功能，智能管理商品和订单处理", "tiktok": "TikTok 商业平台", "tiktokDesc": "连接TikTok商业账户，自动化内容发布和粉丝互动", "gmail": "Gmail 企业邮箱", "gmailDesc": "集成Gmail邮箱系统，自动化邮件管理和客户沟通", "notConnected": "未连接", "platformApiManagement": "平台API管理", "platformApiManagementDesc": "管理您的平台API集成和连接状态", "testMode": "测试模式", "testModeDesc": "当前运行在测试模式下，某些功能可能使用模拟数据", "allPlatforms": "全部平台", "messagingPlatforms": "消息平台", "ecommercePlatforms": "电商平台", "socialMediaPlatforms": "社交媒体平台", "emailServices": "邮件服务", "loadingPlatformStatus": "正在加载平台状态...", "connectNow": "立即连接", "connecting": "连接中...", "accountInfo": "账户信息", "name": "名称", "email": "邮箱", "loadError": "加载错误", "retry": "重试", "loadPlatformStatusError": "加载平台状态失败", "loadPlatformStatusErrorToast": "无法加载平台状态，请稍后重试", "webhook": {"title": "自定义Webhook配置", "description": "描述", "configure": "配置", "events": {"messageReceived": "消息接收", "messageReceivedDesc": "当接收到新消息时", "messageSent": "消息发送", "messageSentDesc": "当消息成功发送时", "userCreated": "用户创建", "userCreatedDesc": "当创建新用户时", "userUpdated": "用户更新", "userUpdatedDesc": "当用户信息更新时", "orderCreated": "订单创建", "orderCreatedDesc": "当下新订单时", "orderUpdated": "订单更新", "orderUpdatedDesc": "当订单状态变更时", "paymentCompleted": "支付完成", "paymentCompletedDesc": "当支付成功处理时", "leadCreated": "线索创建", "leadCreatedDesc": "当生成新线索时", "leadUpdated": "线索更新", "leadUpdatedDesc": "当线索信息变更时", "agentResponse": "代理响应", "agentResponseDesc": "当AI代理提供响应时"}, "eventTypes": "事件类型", "test": "测试", "webhookUrl": "Webhook URL", "webhookUrlRequired": "Webhook URL是必需的", "webhookUrlPlaceholder": "https://your-domain.com/webhook", "webhookUrlDescription": "您的服务器接收Webhook事件的URL端点", "descriptionPlaceholder": "描述这个Webhook的用途...", "signatureSecret": "签名密钥（可选）", "generateSecret": "生成密钥", "secretDescription": "用于验证Webhook请求真实性的密钥，将在请求头中发送", "selectEventTypes": "选择要接收的事件类型", "eventsSelected": "个事件类型已选择。您的Webhook将接收这些事件的通知。", "webhookTest": "Webhook测试", "testDescription": "发送测试请求到您的Webhook端点以验证配置", "sendTestRequest": "发送测试请求", "testing": "测试中...", "openUrl": "打开URL", "testSuccess": "Webhook测试成功！", "testFailed": "测试失败", "status": "状态", "error": "错误", "testTime": "测试时间", "testRequestExample": "测试请求示例：", "cancel": "取消", "saveConfiguration": "保存配置", "saving": "保存中...", "fillUrlAndEvents": "请填写Webhook URL并选择至少一个事件类型", "selectAtLeastOneEvent": "请选择至少一个事件类型", "configurationSuccess": "Webhook配置成功！", "configurationFailed": "Webhook配置失败，请检查URL是否有效", "fillUrlFirst": "请先填写Webhook URL", "testFailedWithStatus": "Webhook测试失败", "testFailedCheckUrl": "Webhook测试失败，请检查URL是否可访问", "copiedToClipboard": "已复制到剪贴板"}, "whatsappTokenRequired": "WhatsApp 使用永久令牌认证，请在平台设置中配置访问令牌。", "connectWhatsApp": "连接 WhatsApp Business", "whatsappInstructions": "请输入您的 WhatsApp Cloud API 永久访问令牌和电话号码 ID。您可以从 Meta for Developers 控制台获取这些信息。", "permanentAccessToken": "永久访问令牌", "enterAccessToken": "请输入您的访问令牌", "phoneNumberId": "电话号码 ID", "enterPhoneNumberId": "请输入您的电话号码 ID", "whereToFind": "在哪里找到这些信息", "visitMetaDeveloper": "访问 Meta for Developers", "selectYourApp": "选择您的应用", "goToWhatsApp": "进入 WhatsApp 产品页面", "findApiSettings": "查找 API 设置中的令牌和 ID", "openMetaDeveloper": "打开 <PERSON><PERSON>", "connectionSuccess": "连接成功", "readyToUse": "现在可以开始使用了", "connectionFailed": "连接失败", "userCancelled": "用户取消了", "authorization": "授权", "popupBlocked": "弹窗被阻止，请允许弹窗后重试", "authTimeout": "授权超时，请重试", "managementInDevelopment": "管理功能开发中", "troubleshootingInDevelopment": "故障排除功能开发中", "featureNotImplemented": "功能尚未实现", "moreIntegrationsComingSoon": "更多集成即将推出"}, "billing": {"title": "计费中心", "subtitle": "管理您的订阅、支付方式和账单历史，轻松控制财务支出", "description": "管理您的订阅、计费和支付方式", "overview": "概览", "subscription": "订阅", "billingHistory": "计费历史", "paymentMethods": "支付方式", "downloadInvoice": "下载发票", "refreshData": "刷新数据", "viewDetails": "查看详情", "managePayment": "管理支付", "thisMonth": "本月", "nextBilling": "下次计费", "paymentStatus": "支付状态", "currentPlan": "当前计划", "current": "当前", "active": "活跃", "lastPaymentSuccessful": "上次支付成功", "monthlyUsageOverview": "月度使用概览", "monitorUsageToAvoidOverage": "监控资源使用情况以避免超额费用", "apiCalls": "API调用", "storageSpace": "存储空间", "aiProcessing": "AI处理", "bandwidthUsage": "带宽使用", "billingAlerts": "计费警报", "apiUsageNearingLimit": "API使用量接近限制", "apiUsageLimitWarning": "您的API使用量已达到月度限制的78%。考虑升级您的计划。", "nextAutoPaymentReminder": "下次自动支付提醒", "upgrade": "升级", "downgrade": "降级", "planComparison": "计划比较", "manage": "管理", "addPaymentMethod": "添加支付方式", "deletePaymentMethod": "删除支付方式", "confirmDeletePayment": "您确定要删除此支付方式吗？", "downloadInvoiceFor": "下载发票", "starter": "入门版", "professional": "专业版", "enterprise": "企业版", "conversations": "对话", "platforms": "平台集成", "teamMembers": "团队成员", "unlimited": "无限制", "basicAnalytics": "基础分析", "emailSupport": "邮件支持", "advancedAnalytics": "高级分析", "prioritySupport": "优先支持", "whatsappIncluded": "包含WhatsApp Business", "customAnalytics": "自定义分析", "dedicatedSupport": "24/7专属支持", "slaGuarantee": "SLA保证", "whiteLabelCustomization": "白标定制", "quickActions": "快速操作", "paymentMethodsAction": "支付方式", "billingHistoryAction": "账单历史", "billingSettingsAction": "账单设置", "confirmUpgrade": "确认升级", "confirmPlanChange": "确认计划变更", "securePayment": "安全支付", "securePaymentDesc": "使用Stripe安全支付处理您的订阅变更", "selectUpgradePlan": "选择升级计划", "nearingUsageLimit": "接近使用限制", "considerUpgrading": "考虑升级您的计划以避免超额费用", "upgradeNow": "立即升级", "recommendedUpgrade": "推荐升级至", "usedThisMonth": "本月已使用", "remaining": "剩余", "autoPayment": "自动付款", "enableAutoPayment": "启用自动付款", "autoPaymentDescription": "每月自动从默认支付方式扣费", "notificationSettings": "通知设置", "emailNotifications": "邮件通知", "usageAlerts": "使用量警告", "settingsSaved": "设置已保存", "demoMode": "演示模式", "manageYourBillingPreferences": "管理您的账单偏好设置", "members": "成员", "annualTotal": "年度总计", "conversationOverage": "超额对话", "additionalTeamMember": "额外团队成员", "additionalPlatform": "额外平台集成", "contactSales": "联系销售", "startFreeTrial": "开始免费试用", "viewPlatformDetails": "查看平台集成和定价详情", "conversationsCount": "对话", "conversationsShort": "对话", "platformsShort": "平台", "teamMembersShort": "团队成员", "perMonth": "/月", "selectUpgrade": "选择升级", "selectDowngrade": "选择降级", "backToSelection": "返回选择", "currentPlanLabel": "当前计划", "newPlanLabel": "新计划", "stripeConfigNotLoaded": "❌ Stripe配置未加载，请稍后重试", "paymentProcessingFailed": "❌ 支付处理失败:", "paymentPageLoadFailed": "❌ 支付页面加载失败:", "paymentPageCreateFailed": "❌ 支付页面创建失败:", "downloadingInvoice": "📄 正在下载发票...", "dataRefreshSuccess": "✅ 数据刷新成功！", "dataRefreshFailed": "❌ 数据刷新失败，请重试", "confirmUpgradeTitle": "确认升级计划", "confirmChangeTitle": "确认更改计划", "planIncludes": "计划包含：", "currentPlanShort": "当前计划", "securePaymentNotice": "💳 安全支付 - 使用Stripe安全支付处理您的订阅更改", "confirmUpgradeButton": "确认升级", "confirmChangeButton": "确认更改", "paymentMethodManagement": "💳 支付方式管理 - 安全管理您的支付信息", "savedPaymentMethods": "已保存的支付方式", "addNewCard": "添加新卡片", "expiryDate": "过期时间", "default": "默认", "billingHistoryManagement": "📄 账单历史 - 查看和管理您的所有账单记录", "billing": {"monthly": "月付", "annualBilling": "年度计费", "save20": "节省 20%"}}, "pricing": {"title": "选择适合您的定价计划", "subtitle": "无论您是个人用户还是大型企业，我们都有完美的解决方案满足您的需求", "monthly": "月付", "yearly": "年付", "annualBilling": "年度计费", "save20": "节省 20%", "plans": {"starter": {"name": "入门版", "description": "适合刚起步的小型企业"}, "professional": {"name": "专业版", "description": "成长型企业的最佳选择"}, "enterprise": {"name": "企业版", "description": "大型组织的完整解决方案"}}, "features": {"basicContextMemory": "基础上下文记忆", "advancedContextMemory": "高级上下文记忆", "enterpriseContextMemory": "企业级上下文记忆", "walkinAppointments": "到店预约管理", "walkinOnsiteAppointments": "到店+上门预约", "completeAppointmentManagement": "完整预约管理", "basicAnalyticsReports": "基础分析报告", "advancedAnalyticsReports": "高级分析与报告", "customReportsDashboards": "自定义报告与仪表板", "emailSupport": "邮件支持", "emailLiveChatSupport": "邮件+在线聊天支持", "dedicatedSupport247": "24/7专属支持", "basicWorkflowsAutomation": "基础工作流自动化", "advancedWorkflowAutomation": "高级工作流自动化", "enterpriseWorkflowAutomation": "企业级工作流自动化", "webChatWidgetIncluded": "包含网页聊天小组件", "multiLanguageSupport": "多语言支持", "apiAccessIntegrations": "API访问与集成", "standardResponseTime": "标准响应时间", "priorityResponseTime": "优先响应时间", "slaGuaranteeUptime": "SLA保证(99.9%正常运行时间)", "basicTemplatesLibrary": "基础模板库", "advancedTemplatesLibrary": "高级模板库", "platformSelectionGuide": "平台选择指南", "priorityPlatformSetup": "优先平台配置", "dedicatedAccountManager": "专属客户经理", "setupAssistance": "设置协助", "dedicatedAccountSupport": "专属客户支持", "executiveBusinessReviews": "高管业务回顾", "basicTrainingMaterials": "基础培训资料", "advancedTrainingProgram": "高级培训计划", "customAiTraining": "自定义AI训练", "customBrandingOptions": "自定义品牌选项", "whiteLabelCustomization": "白标定制", "teamCollaborationTools": "团队协作工具", "advancedReportingDashboard": "高级报告仪表板", "performanceOptimization": "性能优化", "customIntegrationConsulting": "自定义集成咨询", "allPlatformIntegrationsIncluded": "包含所有平台集成", "priorityApiAccess": "优先API访问", "advancedSecurityFeatures": "高级安全功能", "complianceCertifications": "合规认证", "customIntegrations": "自定义集成", "priorityFeatureRequests": "优先功能请求", "customDevelopmentSupport": "自定义开发支持", "enterpriseGradeInfrastructure": "企业级基础设施", "unlimitedPlatformConnections": "无限平台连接"}, "platforms": {"webChatPlusThree": "网页聊天(免费) + 选择任意3个平台", "webChatPlusSix": "网页聊天(免费) + 选择任意6个平台", "allPlatformsPlusCustom": "所有平台 + 自定义集成", "freePlusThree": "免费 + 3个", "freePlusSix": "免费 + 6个", "unlimited": "无限制"}}, "analytics": {"title": "业务分析", "description": "全面的业务分析和数据洞察", "overview": "概览", "modules": "模块", "prediction": "预测", "performance": "性能", "businessAnalytics": "业务分析", "moduleUsageTrends": "模块使用趋势", "usageTrendsDescription": "所有模块的实时使用分析", "performanceComparison": "性能比较", "performanceComparisonDescription": "模块间响应时间和成功率比较", "usageDistribution": "使用分布", "moduleGrowthRate": "模块增长率", "moduleGrowthDescription": "每个模块的相对增长率", "aiChatbot": "AI聊天机器人", "bookingSystem": "预订系统", "contentGeneration": "内容生成", "platformApi": "平台API", "responseTime": "响应时间", "successRate": "成功率", "userSatisfaction": "用户满意度", "conversionRate": "转化率", "exportReport": "导出报告", "last7Days": "过去7天", "last30Days": "过去30天", "last90Days": "过去90天", "all": "全部", "chatBot": "聊天机器人", "booking": "预订", "content": "内容", "api": "API", "aiAutoReply": "AI自动回复", "contentGenerator": "内容生成器", "bookingOnsite": "现场预订", "bookingWalkinService": "现场预订", "leadGeneration": "潜在客户生成", "filterModule": "筛选模块", "usageTrendComparison": "使用趋势比较", "usagePercentageAnalysis": "使用百分比分析", "moduleComparisonAnalysis": "模块比较分析", "performanceComparisonChart": "性能比较图表", "trendPredictionAnalysis": "趋势预测分析", "businessGrowthPrediction": "业务增长预测", "predictionInsights": "预测洞察", "growthForecast": "增长预测", "growthForecastDescription": "基于当前趋势，预计下个季度各模块使用量将提高15-25%", "optimizationRecommendations": "优化建议", "optimizationRecommendationsDescription": "推荐增加AI聊天机器人和内容生成模块的资源配置，以满足日益增长的需求", "keyMetricsPrediction": "关键指标预测", "expectedGrowth": "预期增长", "systemPerformanceTrend": "系统性能趋势", "realTimeSystemPerformanceMonitoring": "实时系统性能监控", "performanceOptimization": "性能优化", "performanceMetricsOptimizationOpportunities": "性能指标优化机会", "highPerformance": "高性能", "systemMaintainingOptimalPerformanceLevels": "系统保持最佳性能水平", "optimizationOpportunity": "优化机会", "apiResponseTimeCanBeFurtherOptimized": "API响应时间可进一步优化", "recommendation": "建议", "considerScalingResources": "考虑扩展资源以提高峰值性能", "moduleUsageDistribution": "模块使用分布", "vsLastPeriod": "与上期相比", "usageCountThisPeriod": "本期使用次数", "throughput": "吞吐量", "businessTrendAnalysis": "业务趋势分析", "loadingBusinessAnalysisData": "正在加载业务分析数据...", "dataLoadingFailed": "数据加载失败", "retry": "重试", "averageApiResponseTime": "平均API响应时间", "businessOperationSuccessRate": "业务操作成功率", "averageUserRating": "平均用户评分", "visitorConversionRate": "访客转化率"}, "agents": {"title": "AI代理管理", "description": "创建、管理和监控您的AI代理", "createAgent": "创建代理", "smartWizard": "智能代理向导", "agentList": "代理列表", "searchAgents": "搜索代理...", "filterByStatus": "按状态筛选", "filterByType": "按类型筛选", "allStatuses": "所有状态", "allTypes": "所有类型", "name": "名称", "type": "类型", "status": "状态", "created": "创建时间", "actions": "操作", "active": "活跃", "inactive": "不活跃", "draft": "草稿", "error": "错误", "deploying": "部署中", "activate": "激活", "deactivate": "停用", "edit": "编辑", "delete": "删除", "view": "查看", "confirmDelete": "您确定要删除此代理吗？", "deleteDescription": "此操作无法撤销。这将永久删除代理及其所有相关数据。", "statusUpdated": "状态已更新", "statusUpdateFailed": "状态更新失败", "agentDeleted": "代理已删除", "deletionFailed": "删除失败", "aiAutoReplyDesc": "智能自动客户消息回复", "bookingWalkinDesc": "现场预约和排队管理", "bookingOnsiteDesc": "在线预约和调度", "contentGeneratorDesc": "智能内容创建和生成", "leadGenerationDesc": "潜在客户获取和管理", "quickCreate": "快速创建", "dragToCreate": "将代理类型拖到这里快速创建", "orUseSmartWizard": "或使用智能向导进行引导创建", "noAgentsFound": "未找到代理", "adjustFilters": "尝试调整搜索或筛选条件", "hideCards": "隐藏卡片", "hideQuickCreate": "隐藏快速创建", "clickCardToCreate": "点击任何卡片", "orDragToCreate": "立即创建，或拖拽到下面的放置区域获取高级选项", "clickToCreateInstantly": "点击立即创建", "dragToCustomize": "高级：拖拽卡片以在创建前自定义", "creatingAgent": "正在创建代理...", "releaseToCreate": "释放以创建", "agentWithCustomSettings": "带有自定义设置的代理", "dropZone": "放置区域", "dragCardHere": "将任何代理卡片拖拽到这里获取高级创建选项", "openMenu": "打开菜单", "agentCreated": "代理已创建", "creationFailed": "创建失败", "smartAgentCreated": "智能代理创建成功", "smartAgentCreatedDesc": "您的智能代理已成功创建和配置。", "creationErrorDesc": "创建代理时发生错误，请重试。", "draftSaved": "草稿保存成功", "draftSavedDesc": "您的代理已保存为草稿，可稍后配置。", "saveFailed": "保存失败", "saveErrorDesc": "保存草稿时发生错误，请重试。", "deleting": "正在删除...", "confirmDeleteButton": "是的，删除代理", "aiAutoReply": "AI自动回复", "contentGenerator": "内容生成器", "bookingOnsite": "预约预订", "bookingWalkin": "上门预订", "leadGeneration": "潜在客户生成", "activating": "正在激活", "deactivating": "正在停用", "smartConfig": {"title": "智能AI配置", "subtitle": "智能引导模式 - 简化配置流程", "expertMode": "专家模式 - 完整配置选项", "switchToExpert": "切换到专家模式", "switchToSmart": "切换到智能模式", "setupProgress": "设置完成度", "scenarios": {"title": "选择业务场景", "subtitle": "选择最符合您业务需求的场景，我们将为您预配置最佳设置", "customerService": "客户服务", "customerServiceDesc": "处理客户咨询、投诉和售后问题", "salesSupport": "销售支持", "salesSupportDesc": "产品介绍、价格咨询和销售引导", "technicalSupport": "技术支持", "technicalSupportDesc": "技术问题解答和故障排除"}, "steps": {"scenario": "选择场景", "basic": "基础设置", "behavior": "行为调优", "advanced": "高级选项"}, "basic": {"title": "基础设置", "subtitle": "配置AI助手的基本信息和行为", "agentName": "助手名称", "agentNamePlaceholder": "例如：小助手、客服小美", "roleDefinition": "角色定义", "roleDefinitionPlaceholder": "描述AI助手的角色和职责...", "roleDefinitionHelp": "告诉AI它是谁，应该如何与客户交流", "responseStyle": "回复风格", "responseStyleProfessional": "专业正式", "responseStyleFriendly": "友好亲切", "responseStyleCasual": "轻松随意"}, "behavior": {"title": "行为调优", "subtitle": "调整AI助手的回复方式和行为", "creativity": "创意度", "creativityPrecise": "精确", "creativityCreative": "创意", "creativityHelp": "控制回复的创意度。较低值产生更一致的回复，较高值产生更多样的回复", "responseLength": "回复长度", "responseLengthShort": "简短精炼", "responseLengthMedium": "中等详细", "responseLengthLong": "详细全面", "escalationThreshold": "转人工门槛", "escalationNever": "从不", "escalationAlways": "总是", "escalationHelp": "当AI置信度低于此阈值时，对话将转给人工客服"}, "preview": {"title": "实时预览", "testMessage": "输入测试消息...", "generating": "正在生成回复...", "placeholder": "在上方输入测试消息，查看AI的回复效果"}, "suggestions": {"title": "智能建议", "warning": "注意", "info": "提示", "systemPromptMissing": "建议设置系统提示词来定义AI的角色和行为", "highCreativity": "创意度较高，回复可能更多样但一致性较低"}}, "flowBuilder": {"title": "可视化流程构建器", "subtitle": "通过拖拽界面设计对话流程", "createNew": "创建新流程", "saveFlow": "保存流程", "publishFlow": "发布流程", "testFlow": "测试流程", "exportFlow": "导出流程", "importFlow": "导入流程", "flowName": "流程名称", "flowDescription": "流程描述", "flowNamePlaceholder": "输入流程名称...", "flowDescriptionPlaceholder": "描述此流程的功能...", "nodeTypes": {"trigger": "触发器", "condition": "条件判断", "ai_response": "AI回复", "human_handoff": "转人工", "api_call": "API调用", "delay": "延时", "webhook": "Webhook", "email": "邮件", "sms": "短信", "variable_set": "设置变量", "jump": "跳转", "end": "结束"}, "nodeCategories": {"trigger": "触发器", "logic": "逻辑", "action": "动作", "integration": "集成", "utility": "工具"}, "toolbar": {"addNode": "添加节点", "deleteNode": "删除节点", "copyNode": "复制节点", "pasteNode": "粘贴节点", "undo": "撤销", "redo": "重做", "zoomIn": "放大", "zoomOut": "缩小", "fitToScreen": "适应屏幕", "toggleMinimap": "切换小地图", "toggleGrid": "切换网格"}, "validation": {"title": "流程验证", "noErrors": "未发现错误", "errorsFound": "发现{{count}}个错误", "warningsFound": "发现{{count}}个警告", "missingConnection": "节点缺少输出连接", "invalidConfig": "节点配置无效", "circularReference": "检测到循环引用", "unreachableNode": "节点从起点无法到达"}, "templates": {"title": "流程模板", "useTemplate": "使用模板", "customerService": "客户服务", "salesInquiry": "销售咨询", "technicalSupport": "技术支持", "leadQualification": "线索筛选", "appointmentBooking": "预约预订"}}, "crmIntegration": {"title": "CRM集成中心", "subtitle": "连接您喜爱的CRM和业务工具", "addIntegration": "添加集成", "manageConnections": "管理连接", "syncNow": "立即同步", "testConnection": "测试连接", "viewLogs": "查看日志", "configure": "配置", "disconnect": "断开连接", "reconnect": "重新连接", "status": {"connected": "已连接", "disconnected": "已断开", "error": "错误", "pending": "待处理", "syncing": "同步中"}, "categories": {"all": "所有集成", "crm": "CRM系统", "marketing": "营销工具", "sales": "销售工具", "support": "支持工具", "analytics": "分析工具"}, "providers": {"salesforce": "Salesforce", "hubspot": "HubSpot", "pipedrive": "Pipedrive", "zapier": "Zapier"}, "features": {"contacts": "联系人管理", "leads": "线索管理", "opportunities": "机会跟踪", "accounts": "客户管理", "cases": "案例管理", "workflows": "工作流自动化"}, "setup": {"title": "设置集成", "selectProvider": "选择提供商", "configure": "配置连接", "authenticate": "身份验证", "mapFields": "字段映射", "testSync": "测试同步", "complete": "完成设置"}, "sync": {"title": "同步设置", "enabled": "启用同步", "direction": "同步方向", "frequency": "同步频率", "bidirectional": "双向同步", "toCrm": "仅同步到CRM", "fromCrm": "仅从CRM同步", "realTime": "实时", "hourly": "每小时", "daily": "每天", "weekly": "每周", "manual": "仅手动"}, "health": {"title": "连接健康状态", "healthy": "健康", "warning": "警告", "error": "错误", "lastChecked": "最后检查", "responseTime": "响应时间", "successRate": "成功率", "errorRate": "错误率"}}, "analytics": {"title": "高级分析", "subtitle": "全面的洞察和性能指标", "overview": "概览", "funnels": "对话漏斗", "sentiment": {"title": "情感分析", "subtitle": "监控客户情感和满意度", "overall": "整体情感", "trends": "情感趋势", "topics": "话题分析", "alerts": "情感警报", "positive": "正面", "neutral": "中性", "negative": "负面", "score": "情感评分"}, "performance": {"title": "性能指标", "subtitle": "系统和AI性能指标", "responseTime": "响应时间", "throughput": "吞吐量", "availability": "可用性", "aiMetrics": "AI指标", "uptime": "运行时间", "accuracy": "准确性", "confidence": "置信度"}, "journeys": "用户旅程", "reports": "自定义报告", "exports": "数据导出", "createReport": "创建报告", "createDashboard": "创建仪表板", "exportData": "导出数据", "timeRange": {"last24h": "过去24小时", "last7d": "过去7天", "last30d": "过去30天", "last90d": "过去90天", "custom": "自定义范围"}, "metrics": {"totalConversations": "总对话数", "completedConversations": "完成对话数", "averageResponseTime": "平均响应时间", "satisfactionScore": "满意度评分", "resolutionRate": "解决率", "escalationRate": "升级率", "sentimentScore": "情感评分", "positiveRate": "正面率", "negativeRate": "负面率"}, "funnel": {"title": "对话漏斗", "subtitle": "跟踪用户在对话阶段的进展", "steps": "步骤", "conversionRate": "转化率", "dropoffRate": "流失率", "averageTime": "平均时间", "totalEntries": "总进入数", "completions": "完成数"}}, "intelligentOptimization": {"title": "AI驱动优化", "subtitle": "智能性能优化和A/B测试", "overview": "概览", "abTesting": "A/B测试", "parameterTuning": "参数调优", "predictions": {"title": "预测分析", "subtitle": "AI驱动的性能预测", "forecast": "预测", "trends": "趋势", "anomalies": "异常", "alerts": "警报", "accuracy": "模型准确性", "nextPrediction": "下次预测", "confidenceInterval": "置信区间"}, "recommendations": {"title": "智能建议", "subtitle": "AI生成的优化建议", "pending": "待处理", "implemented": "已实施", "rejected": "已拒绝", "impact": "影响", "effort": "工作量", "implement": "实施", "reject": "拒绝", "viewDetails": "查看详情"}, "createTest": "创建A/B测试", "createOptimization": "创建优化", "viewResults": "查看结果", "status": {"active": "活跃", "paused": "暂停", "disabled": "禁用", "running": "运行中", "completed": "已完成", "draft": "草稿"}, "abTest": {"title": "A/B测试引擎", "subtitle": "自动化测试以获得最佳性能", "createNew": "创建新测试", "variants": "变体", "metrics": "指标", "results": "结果", "winner": "获胜者", "confidence": "置信度", "significance": "显著性", "trafficSplit": "流量分配", "testDuration": "测试持续时间", "sampleSize": "样本大小"}, "optimization": {"title": "参数优化", "subtitle": "AI驱动的参数调优", "autoTuning": "自动调优", "manualTuning": "手动调优", "currentValue": "当前值", "suggestedValue": "建议值", "expectedImprovement": "预期改进", "applyChanges": "应用更改", "revertChanges": "撤销更改"}}}, "systemMonitoring": {"title": "系统监控仪表板", "description": "实时系统性能和健康监控", "viewDetailedReport": "查看详细报告", "cpuUsage": "CPU使用率", "memoryUsage": "内存使用率", "storageSpace": "存储空间", "networkLatency": "网络延迟", "currentProcessorUtilization": "当前处理器利用率", "systemMemoryConsumption": "系统内存消耗", "diskStorageUtilization": "磁盘存储利用率", "averageResponseLatency": "平均响应延迟", "healthy": "健康", "warning": "警告", "critical": "严重", "systemStatus": "系统状态", "allSystemsOperational": "所有系统正常运行", "serviceStatus": "服务状态", "uptime": "运行时间", "response": "响应", "version": "版本", "lastCheck": "最后检查", "justNow": "刚刚", "minAgo": "分钟前", "resourceDistribution": "资源分布", "performanceTrends": "24小时性能趋势", "viewDetailedAnalytics": "查看详细分析", "apiGateway": "API网关", "coreService": "核心服务", "aiIntelligenceService": "AI智能服务", "clientApplication": "客户端应用", "cpu": "CPU", "memory": "内存", "storage": "存储", "network": "网络", "cores": "核心", "usage": "使用率", "total": "总计", "current": "当前", "threshold": "阈值", "port": "端口", "systemLogs": "系统日志", "businessAnalytics": "业务分析", "systemResourceUsageOverview": "系统资源使用概览", "realTimeSystemPerformanceMonitoring": "实时系统性能监控", "currentStatusOfAllPlatformServices": "所有平台服务的当前状态", "of": "的"}, "dataOverview": {"title": "数据概览", "description": "关键数据指标和性能指标的综合概览", "totalRevenue": "总收入", "revenueChange": "较上月+20.1%", "activeUsers": "活跃用户", "userChange": "较上月+180.1%", "newOrders": "新订单", "orderChange": "较上月+19%", "systemHealth": "系统健康", "uptimeValue": "99.2%", "lastCheck": "2分钟前", "revenueTrends": "收入趋势", "monthlyRevenueOverview": "月度收入增长和分析", "userGrowth": "用户增长", "userSignupsOverTime": "用户注册和增长模式", "chartPlaceholder": "图表将在此处显示", "dataUpdatedHourly": "数据每小时更新", "realTimeUpdates": "实时更新", "recentActivities": "最近活动", "recentActivitiesDescription": "最新用户活动和系统事件", "dataTablePlaceholder": "数据表将在此处显示", "showingLastEntries": "显示最近100条记录", "viewAll": "查看全部"}, "marketingCampaigns": {"title": "营销活动", "description": "创建、管理和跟踪您的营销活动。", "createNewCampaign": "创建新活动", "allCampaigns": "所有活动", "active": "活跃", "scheduled": "已安排", "drafts": "草稿", "completed": "已完成", "archived": "已归档", "noCampaignsMatchFilters": "没有活动匹配您的过滤条件", "createFirstCampaign": "创建您的第一个活动以开始。", "tryDifferentFilter": "尝试不同的过滤器或创建新活动。", "viewEdit": "查看/编辑", "viewReport": "查看报告", "duplicate": "复制", "archive": "归档", "delete": "删除", "targetAudience": "目标受众", "scheduledFor": "安排时间", "activeDates": "活动日期", "ongoing": "进行中", "keyMetrics": "关键指标", "viewDetails": "查看详情"}, "settingsLanding": {"title": "设置中心", "autoReplyTitle": "自动回复设置", "autoReplyDesc": "配置自动回复规则和行为。", "goToAutoReply": "转到自动回复设置", "walkinBookingTitle": "现场预订设置", "walkinBookingDesc": "管理现场预订选项和配置。", "goToWalkinBooking": "转到现场预订设置", "onsiteBookingTitle": "现场预订设置", "onsiteBookingDesc": "管理现场预订选项和配置。", "goToOnsiteBooking": "转到现场预订设置", "contentGeneratorTitle": "内容生成器设置", "contentGeneratorDesc": "配置品牌和内容生成设置。", "goToContentGenerator": "转到内容生成器设置", "platformApiTitle": "平台API管理", "platformApiDesc": "连接和管理第三方平台集成。", "goToPlatformApi": "转到平台API管理"}, "chatPage": {"title": "智能预约助手", "subtitle": "AI驱动的智能预约和客服系统", "welcomeMessage": "您好！我是iBuddy智能预约助手，可以帮您处理以下服务：\\n\\n🏪 现场排队预约\\n🚗 上门服务预约\\n📋 查询预约状态\\n⚙️ 服务设置\\n\\n请选择您需要的服务，或直接告诉我您的需求！", "walkinBooking": "现场排队", "walkinDescription": "加入现场服务队列", "onsiteBooking": "预约上门", "onsiteDescription": "安排技师上门服务", "checkBooking": "查询预约", "checkDescription": "查看预约状态", "contactSupport": "联系客服", "supportDescription": "人工客服支持", "walkinResponse": "正在为您安排现场排队服务...\\n\\n📍 请到店后扫描二维码或告知工作人员您的手机号码\\n⏰ 当前预计等待时间：15分钟\\n👥 当前排队人数：3人\\n\\n您的排队号码是：A008", "basicConsultation": "基础咨询", "onsiteResponse": "为您安排上门服务预约...\\n\\n📅 请选择服务时间\\n📍 请提供详细地址\\n🔧 可选服务：家电维修、技术支持、安装调试\\n💰 上门费：根据距离计算\\n\\n请提供更多详细信息以确认预约。", "checkResponse": "为您查询预约信息...\\n\\n📋 近期预约：\\n1. 现场服务 - 今日 14:30 (已完成)\\n2. 上门维修 - 明日 10:00 (待确认)\\n\\n如需详细信息，请提供预约编号。", "supportResponse": "正在为您转接人工客服...\\n\\n📞 客服热线：400-888-8888\\n⏰ 服务时间：9:00-21:00\\n💬 在线客服已启动，稍后会有专员为您服务。", "defaultResponse": "抱歉，我不理解这个操作。请选择其他服务或直接描述您的需求。", "selectedAction": "选择了：{{action}}", "keywordBooking": "预约", "keywordQueue": "排队", "keywordOnsite": "上门", "keywordRepair": "维修", "keywordCheck": "查询", "keywordStatus": "状态", "keywordCancel": "取消", "keywordSupport": "客服", "keywordHuman": "人工", "onsiteScheduleResponse": "我来为您安排上门服务预约。请告诉我：1. 需要什么服务？（维修、安装、咨询等）2. 希望的服务时间 3. 服务地址。或者您可以点击预约上门按钮快速预约。", "walkinScheduleResponse": "我来为您安排现场排队服务。当前服务信息：服务地点：XXX营业厅，营业时间：9:00-18:00，当前排队：3人，预计等待：15分钟。点击现场排队按钮即可加入队列。", "statusCheckResponse": "请提供您的预约编号或登记的手机号码，我来为您查询预约状态。也可以点击查询预约按钮查看您的所有预约记录。", "cancelResponse": "我来帮您取消预约。请提供：1. 预约编号 2. 登记的手机号码。确认信息后我会为您处理取消手续。", "humanSupportResponse": "正在为您转接人工客服，请稍候...客服将在1-2分钟内与您联系。如需紧急服务，可直接拨打400-888-8888", "generalResponse": "收到您的消息。我可以帮您处理：现场排队预约、上门服务预约、查询预约状态、修改或取消预约。请选择您需要的服务，或告诉我具体需求。", "serviceType": "服务类型", "time": "时间", "address": "地址", "smartChat": "智能对话", "bookingService": "预约服务", "serviceStatus": "服务状态", "assistantName": "iBuddy 智能助手", "online": "在线", "inputPlaceholder": "输入您的消息..."}}